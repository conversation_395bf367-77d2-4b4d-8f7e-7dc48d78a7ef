<div class="d-flex justify-content-between align-items-end flex-wrap">
    <div class="mr-1 mb-2">
        <a href="{{ route('versions.index', ['refresh' => 'true']) }}"
            class="btn btn-default text-white d-none d-sm-inline-block">
            <i class="fal fa-sync"></i> {{ trans('language.refresh') }}
        </a>
        <a href="#card-filter" class="btn btn-default text-white" data-toggle="collapse">
            <i class="far fa-filter"></i> {{ trans('language.filter') }}
        </a>
    </div>
    <div class="actions mb-2">
        <a href="{{ route('versions.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {{ trans('language.add_new') }}
        </a>
    </div>
</div>

<div class="collapse" id="card-filter">
    <div class="card mb-3">
        <div class="card-body border-0">
            <form action="{{ route('versions.index') }}" method="GET">
                <div class="row">
                    <div class="col-md-6 col-xl-4 mb-3">
                        <input class="form-control" type="text" name="search" value="{{ request('search') }}"
                            placeholder="{{ __('language.version_search_placeholder') }}">
                    </div>

                    <!-- Status Filter -->
                    <div class="col-md-6 col-xl-4 mb-3">
                        <select name="status" class="form-control select2-base">
                            <option value="">{{ __('language.status_default') }}</option>
                            <option value="{{ \App\Models\AppVersion::DRAFT }}"
                                {{ request('status') === (string) \App\Models\AppVersion::DRAFT ? 'selected' : '' }}>
                                {{ __('language.draft') }}
                            </option>
                            <option value="{{ \App\Models\AppVersion::PUBLISHED }}"
                                {{ request('status') === (string) \App\Models\AppVersion::PUBLISHED ? 'selected' : '' }}>
                                {{ __('language.published') }}
                            </option>
                        </select>
                    </div>

                    <!-- Platform Filter -->
                    <div class="col-md-6 col-xl-4 mb-3">
                        <select name="platform" class="form-control select2-base">
                            <option value="">{{ __('language.device_type_default') }}</option>
                            <option value="{{ \App\Models\AppVersion::IOS }}"
                                {{ request('platform') === (string) \App\Models\AppVersion::IOS ? 'selected' : '' }}>
                                {{ __('language.ios') }}
                            </option>
                            <option value="{{ \App\Models\AppVersion::ANDROID }}"
                                {{ request('platform') === (string) \App\Models\AppVersion::ANDROID ? 'selected' : '' }}>
                                {{ __('language.android') }}
                            </option>
                        </select>
                    </div>

                    <!-- Filter Button -->
                    <div class="col-12 text-right">
                        <button type="submit" class="btn btn-primary button-filter" style="width: 158px">
                            <i class="fas fa-filter"></i> {{ trans('language.filter') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
