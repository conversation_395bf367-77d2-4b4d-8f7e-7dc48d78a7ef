import {readFileImage} from "../helper/FormValid";
import locales from "../locales/locales";
let language = $("body").data('locales'),
    trans = locales(language);
// main export
export default function FormCustom(){
    // events
    $(document).on('change', '.form-image__file', readFileImage);
    $(document).on('change', 'select[name="project"], select[name="project_id"]', clearValueSelectMember);

    $(document).on('input', '.input-range-group input[type="range"]', changeInputRange);

    // upload file
    let dropZones = $('.input-file-group');
    if (dropZones.length){
        dropZones.each(function () {
            let dropzoneControl = $(this)[0].dropzone;
            if (dropzoneControl) {
                return;
            }
            inputFileDropzone($(this));
        });
    }

    //reset Form
    handleResetForm();

    //Format summernote textarea
    $("form").on("submit", formatSummernote);
    $(document).on('change', 'input[type="file"]', function(){
        $('.input-file-group #previews small.text-danger').text('');
    })
}

// custom input type="range"
function changeInputRange(e){
    let value = +e.target.value;
    let label = $('.'+$(this).attr('id')+'_value');
    label.text(value);
}

//hanlding reset form
function handleResetForm(){
    $('form').each(function () {
        let form = $(this),
            reset = form.find(':reset');

        let resetForm = () => {
            setTimeout(function () {
                let inputs = form.find(':input');

                if (form.find('.summernote').length){
                    form.find('.summernote').each(function (){
                        $(this).summernote("code", $(this).val());
                    })
                }
                inputs.trigger('change');
            }, 50);
        }
        reset.on('click', resetForm);
    });
}
const TYPE = {
    IMG: 1,
    FILE: 2 
}
function inputFileDropzone(dz){
    let upload_url = dz.data('upload-url'),
        remove_url = dz.data('remove-url'),
        upload_element = dz.attr('upload-element'),
        id = dz.attr('id'),
        has_button_submit = dz.data('check-file'),
        fileSize = dz.data('file-size'),
        elementError = dz.data('element-error'),
        requireImg = dz.data('require-img');

    // DropzoneJS Demo Code Start
    Dropzone.autoDiscover = false;
    const appendConfig = requireImg ? {
        acceptedFiles: 'image/*'
    } : {};
    // Get the template HTML and remove it from the doumenthe template HTML and remove it from the doument
    let previewNode = document.querySelector("#template");
    previewNode.id = "";
    let previewTemplate = previewNode.parentNode.innerHTML;
    previewNode.parentNode.removeChild(previewNode);
    let myDropzone = new Dropzone("#" + id, { // Make the whole body a dropzone
        url: '#', // Set the url
        thumbnailWidth: 80,
        thumbnailHeight: 80,
        parallelUploads: 20,
        previewTemplate: previewTemplate,
        ...appendConfig,
        autoQueue: false, // Make sure the files aren't queued until manually added
        previewsContainer: "#" + id + " #previews", // Define the container to display the previews
        clickable:"#" + id + " .fileinput-button", // Define the element that should be used as click trigger to select files.
        init: function(){
            if(has_button_submit){
                let submit_file = document.getElementById('submit');
                submit_file.disabled = true
            };
        }
    });
    $(document).off('paste', `textarea[data-id="${id}"] + .note-editor .note-editable`).on('paste', `textarea[data-id="${id}"] + .note-editor .note-editable`, function(event) {
        event.preventDefault();
        var note = $(this);
        var summernote = $(this).closest(".note-editor").prev('.summernote');
        var bufferText = ((event.originalEvent || event).clipboardData || window.clipboardData).getData('Text');

        if (summernote.data('id') == id) {
            var items = (event.clipboardData || event.originalEvent.clipboardData).items;
           

            if (items.length>0) {
                // paste image
                if ((items[0].kind === 'file')) {
                    $.each(items, function(index) {
                        var item = items[index];
                        var file = item.getAsFile();
                        if (item.kind === 'file' && (file['type'] == 'image/jpeg' || file['type'] == 'image/png' || file['type'] == 'image/jpg')) {
                            addImage();
                            if (file['name'] =='image.png') {
                                var blob = file.slice(0, file.size, 'image/png');
                                file = new File([blob], 'clipboard-' + Date.now() + '.png', {type: 'image/png'});
                            }
                            file.resize = true;
                            // adds the file to your dropzone instance
                            myDropzone.addFile(file);
                        }
                    })
                } else {
                    items[0].getAsString(function(value){
                        var urlRegex = /^(http|ftp|https):\/\/[\w-]+(\.[\w-]+)+([\w.,@?^=%&amp;:\/~+#-]*[\w@?^=%&amp;\/~+#-])?/;
                        if (urlRegex.test(value)) {
                            // paste url
                            const selection = window.getSelection();
                            if (!selection.rangeCount) return false;
                            selection.deleteFromDocument();
                            var node = document.createElement('a');
                            node.innerHTML = value;
                            node.setAttribute("href", value);
                            var range = selection.getRangeAt(0);
                            range.collapse(false);
                            range.insertNode(node);
                            range = range.cloneRange();
                            range.selectNodeContents(node);
                            range.collapse(false);
                            selection.removeAllRanges();
                            selection.addRange(range);
                        } else {
                            document.execCommand('insertText', false, bufferText);
                        }
                    })
                }
            }
        }
    });

    $(document).off('drop', `textarea[data-id="${id}"] + .note-editor`).on('drop', `textarea[data-id="${id}"] + .note-editor`, function(event) {
		var note = $(this).find(".note-editable");
        var summernote = $(this).prev('.summernote');
        if (summernote.data('id') == id) {
            var items = (event.dataTransfer || event.originalEvent.dataTransfer).items;
            if ((items[0].kind === 'file')) {
                $.each(items, function(index) {
                    var item = items[index];
                    var file = item.getAsFile();
                    if (item.kind === 'file' && (file['type'] == 'image/jpeg' || file['type'] == 'image/png' || file['type'] == 'image/jpg')) {
                        addImage();
                        if (file['name'] =='image.png') {
                            var blob = file.slice(0, file.size, 'image/png');
                            file = new File([blob], 'clipboard-' + Date.now() + '.png', {type: 'image/png'});
                        }
                        file.resize = true;
                        // adds the file to your dropzone instance
                        myDropzone.addFile(file);
                    }
                })
            }
        }
    });
    
    myDropzone.on("addedfile", function (file) {
        if(fileSize && elementError){
        // phần check này của mục tài sản
            const maxSize = +fileSize * 1024 * 1024;
            let isError = false
            if(requireImg){
                const check = checkTypeFileImage(file);
                if(!check){
                    $(elementError).text(trans.mimes_file.replace(":attribute", trans.asset_image).replace(":values", 'jpeg,png,jpg,gif,svg,jfif,bmp,tiff,pjpeg'));
                    isError = TYPE.IMG
                }else{
                    if(file["size"] > maxSize){
                        $(elementError).text(trans.max_file.replace(":attribute", trans.asset_image).replace(":max", fileSize));
                        isError = TYPE.IMG
                    }
                };
            }else{
                if(file["size"] > maxSize ){
                    $(elementError).text(trans.max_file.replace(":attribute", trans.asset_file).replace(":max", fileSize));
                    isError = TYPE.FILE
                }
            }
            if(isError){
                $(file.previewElement).attr('data-inValid', isError)
            }
        }
        let inputFileItem = $(this)[0]['previewsContainer']['lastChild'].querySelector("#progress-upload .progress-bar");
        let token = $('meta[name="csrf-token"]').length ? $('meta[name="csrf-token"]').attr('content') : '';
        let fd = new FormData();
        fd.append('file',file);
        if(file.resize){
            fd.append('resize',file.resize);
        }
        $.ajax({
            xhr: function() {
                var jqXHR = null;
                if ( window.ActiveXObject )
                {
                    jqXHR = new window.ActiveXObject( "Microsoft.XMLHTTP" );
                }
                else
                {
                    jqXHR = new window.XMLHttpRequest();
                }

                //Upload progress
                jqXHR.upload.addEventListener( "progress", function ( evt )
                {
                    if ( evt.lengthComputable )
                    {
                        let percentComplete = Math.round( (evt.loaded * 100) / evt.total );
                        inputFileItem.style.width = percentComplete - 10 + "%";
                        inputFileItem.innerHTML = percentComplete - 10 + "%";
                    }
                }, false );


                return jqXHR;
            },
            headers: {
                'X-CSRF-TOKEN': token
            },
            url: upload_url,
            type: 'POST',
            data: fd,
            dataType: 'JSON',
            contentType: false,
            cache: false,
            processData: false,
            success: function success(response) {
                let file_input = file.previewElement;
                inputFileItem.style.width =  "100%";
                inputFileItem.innerHTML =  "100%";
                $(file_input).append("<input type='hidden' id='check_file' name='" + upload_element + "[]' value='" + JSON.stringify(response.data) + "'>");
                let file_name = response.data['file_path'].split('/');
                let url   = window.location.origin + '/tmp/' + file_name[1];
                $(".note-editable img:not([src])").first().attr('src', url).attr('image', file_name[1]);
                if(has_button_submit){
                    let submit_file = document.getElementById('submit');
                    let check_file = document.getElementById('check_file');
                    if(check_file != null){
                        submit_file.disabled = false;
                    }
                };
            },
            error: function error(err) {
            }
        });
    });

    myDropzone.on("removedfile", function (file) {
        let token = $('meta[name="csrf-token"]').length ? $('meta[name="csrf-token"]').attr('content') : '';
        let document_value = $(file.previewElement).find('input[name="'+upload_element+'[]"]').val();
        let checkFileValid = $(file.previewElement).attr('data-inValid');
        // phần check này của mục tài sản
        if(checkFileValid){
            if(checkFileValid == TYPE.IMG){
                const totalFIle = $('#asset_image').find('#previews .input-file-group__template[data-inValid]');
                if(totalFIle.length < 1)  $('.image_error_type_max').text('');
            }else if(checkFileValid == TYPE.FILE){
                const totalFIle = $('#asset_file').find('#previews .input-file-group__template[data-inValid]');
                if(totalFIle.length < 1)  $('.file_error_type_max').text('');
            }
        }
        let file_path = JSON.parse(document_value).file_path;
        let file_name = file_path.split('/');
        $('.note-editable').find("img[image='"+file_name[1]+"']").remove() ;
        if(fileSize && elementError){
            if($('#previews .input-file-group__template').length < 1){
                $(elementError).text('')
            }
        }
        let fd = new FormData();
        fd.append('file_path',file_path);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': token
            },
            url: remove_url + '?' + $.param({
                'file_path': file_path
            }),
            type: 'DELETE',
            contentType: false,
            cache: false,
            processData: false,
            success: function success(response) {
                if(has_button_submit){
                    let submit_file = document.getElementById('submit');
                    let check_file = document.getElementById('check_file');
                    if(check_file == null){
                        submit_file.disabled = true;
                    }
                };
            },
            error: function error(err) {
            }
        });
    });

    myDropzone.on("sending", function (file) {
        // Show the total progress bar when upload starts
        document.querySelector("#total-progress").style.opacity = "1";
        // And disable the start button
        file.previewElement.querySelector(".start").setAttribute("disabled", "disabled");
    });
    // Hide the total progress bar when nothing's uploading anymore
    myDropzone.on("queuecomplete", function (progress) {
        document.querySelector("#total-progress").style.opacity = "0";
    });
}

function clearValueSelectMember(){
    let project = $(this),
        member = project.closest('form');

    if (member.length){
        member.find('.select2-data-users').val('').trigger('change');
    }
}

function formatSummernote() {
    // Fix error missing image
    $(this).find(".note-editable").find("span:not([style])").each(function() {
        $(this).replaceWith($(this).text());
    });
    // Add space 
    $(this).find(".summernote").each(function() {
        $(this).val($(this).summernote('code'));
    }); 
}

function addImage() {
    const selection = window.getSelection();
    if (!selection.rangeCount) return false;
    selection.deleteFromDocument();
    var node = document.createElement('img');
    var range = selection.getRangeAt(0);
    range.collapse(false);
    range.insertNode(node);
    node = document.createTextNode("     ");
    range = selection.getRangeAt(0);
    range.collapse(false);
    range.insertNode(node);
    range = range.cloneRange();
    range.selectNodeContents(node);
    range.collapse(false);
    selection.removeAllRanges();
    selection.addRange(range);
}
function checkTypeFileImage(file) {
    var fileType = file["type"];
    var validImageTypes = [
        "image/jpeg",
        "image/png",
        "image/jpg",
        "image/gif",
        "image/svg+xml",
        "image/bmp",
        "image/webp",
        "image/tiff",
        "image/x-icon",
        "image/vnd.microsoft.icon",
        "image/vnd.wap.wbmp",
        "image/pjpeg",
        "image/x-png"
    ];
    return !($.inArray(fileType, validImageTypes) < 0)
}