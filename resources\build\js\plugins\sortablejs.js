import Sortable from 'sortablejs';
import toastr from 'toastr';
import locales from "../locales/locales";

let language = $("body").data('locales'),
    trans = locales(language);


export function SortableProjectsPinSidebar(){
    let idElm = 'sortable-projects-pin';
    let container = $('#' + idElm);

    if (document.getElementById(idElm) == null) return;

    let sortable = new Sortable(document.getElementById(idElm), {
        animation: 150,
        onUpdate: function(evt) {
            let item = $(evt.item),
                itemCurrent = item.data('pin'),
                itemPrev = item.prev().length && item.prev().data('pin') ? item.prev().data('pin') : 0,
                itemNext = item.next().length && item.next().data('pin') ? item.next().data('pin') : 0;

            let token = $('meta[name="csrf-token"]').length ? $('meta[name="csrf-token"]').attr('content') : '';
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': token
                },
                url: container.data('url'),
                type: 'POST',
                dataType: "JSON",
                data: {
                    itemPrev: itemPrev,
                    itemCurrent: itemCurrent,
                    itemNext: itemNext,
                },
                success: function (data){
                    if (data.status !== 200){
                        toastr.error(data.msg.title, {timeOut: 5000})
                    }
                },
                error: function (err){
                    toastr.error(trans.has_error, {timeOut: 5000})
                }
            })
        }
    });
}