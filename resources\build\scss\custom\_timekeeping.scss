.table-responsive {
    #table_timekeeping {
        &.table-custom thead {
            background: #FFFFFF;
        }

        thead tr th:nth-child(1),
        thead tr th:nth-child(2) {
            background: #fff;
        }

        &.table tr {
            background: #FFFFFF;
            border-bottom: 0;
        }

        &.table tr {
            background: #F5F5F5;
            border-bottom: 0;
        }

        &.table-custom.table-striped tbody tr:nth-of-type(odd) td:nth-child(2),
        &.table-custom.table-striped tbody tr:nth-of-type(odd) td:nth-child(1) {
            background: #fff;
        }

        &.table-custom.table-striped tbody tr:nth-of-type(odd) {
            background-color: #fff;
        }

        &.table-custom tbody tr:hover, .table-custom tbody tr:nth-of-type(odd):hover,
        &.table-custom.table-striped tbody tr:hover,
        &.table-custom.table-striped tbody tr:nth-of-type(odd):hover {
            background: #fff;
        }
        td.change-timekeeping-xxx {
            background: #FFFFFF 0% 0% no-repeat padding-box;
            min-width: 100px;
            position: relative;
        }

        td.change-timekeeping__data {
            background: #F5F5F5 0% 0% no-repeat padding-box;
        }
        td.change-timekeeping__nodata {
            background: #F5F5F5 0% 0% no-repeat padding-box;
        }
        td.change-timekeeping__today {
            background: #FFFFFF 0% 0% no-repeat padding-box;
            border-left: 1px solid #0791A3;
            border-right: 1px solid #0791A3 !important;
        }
        td.change-timekeeping-xxx.change-timekeeping__weekend{
            background-color: #ECECEC !important;
        }
        td.change-timekeeping__today:last-child {
            background-color: red;
        }
        td.change-timekeeping__today {
            border-bottom: 0;
        }
        .change-timekeeping__line-nodata {
            width: 64px;
            height: 1px;
            background: #dee2e6;
            margin-left: 10px;
            margin-top: 25px;
        }
        .th-past_date {
          p{
            background: #F5F5F5 !important;
            margin: 0;
            margin-top: 6px;
            border-top: 1px solid #DDDFE1;
            padding-top: 18px;
            padding-bottom: 13px;
          }
        }
        .th-weekend{
          p{
            color: #7C7C7C !important;
            background: #ECECEC !important;
            margin: 0;
            border-top: 1px solid #DDDFE1;
            padding-top: 18px;
            padding-bottom: 13px;
          }
        }
        .th-today_date{
          border-right: none;
          p{
            border-left: 1px solid #0791A3;
            border-right: 1px solid #0791A3 ; 
            border-top: 1px solid #0791A3 !important; 
            margin: 0;
            padding-top: 18px;
            padding-bottom: 13px;
          }
        }
        .th-feature-date{
          p{
            margin: 0; 
            border-top: 1px solid #DDDFE1;
            padding-top: 18px;
            padding-bottom: 13px;
          }
        }
        .box-timekeeping{
            position: absolute;
            display: flex;
            flex-wrap: wrap;
            top: 6px;
            .icon-locale-checkin{
                position: absolute;
                right: -22px;
                top: 6px;
            }
            .icon-locale-checkout{
                position: absolute;
                top: 29px;
                right: 4px;
            }
        }
        .check-out{
            width: 63px;
            display: flex ;
            justify-content: center;
            .line-danger{
                width: 8px;
                height: 1px;
                background: #FF0000;
                margin-top: 9px;  
            }
        }
        .check-in{
            width: 63px;
            display: flex ;
            justify-content: center;
            .line-danger{
                width: 8px;
                height: 1px;
                background: #FF0000;
                margin-bottom: 9px;
                margin-top: 14px;
            }
        }
        .dayHasNotLocaltion{
          .box-timekeeping{
            //justify-content: center;
            //margin-right: 10px;
          }
        }
        .td-username{
          max-width:262px;color:#088493
        }
      }
}
.modal-add-timekeeping{
    .modal-header {
      background: #0791A3;
      color: #fff;
      padding: 13px 20px 13px 20px;
      button{
        opacity: 1;
      }
    }
    .modal-body{
      padding: 33px 20px 20px 24.5px;
      padding-right: 15px;
      overflow: hidden;
      .scoll{
        max-height: calc(70vh - 240px); 
        overflow-y: auto;
        padding-right: 10px;
        padding-left: 15px;
        margin-right: 0;
        position: relative;
      }
      .time-line{
        width: 1px;
        height: 192px;
        background: #4D4D4D;
        box-shadow: 0px 3px 6px #00000029;
        top: 10px;
        left: 5px;
        position: absolute;
        z-index: 1;
        &::before,
        &::after {
          content: '';
          width: 9px; 
          height: 9px; 
          background: #4d4d4d; 
          border-radius: 50%; 
          position: absolute;
          left: 1px;
          transform: translateX(-50%);
        }
        &::before {
          top: -5px; 
        }
  
        &::after {
          bottom: -5px;
        }
      } 
      .middle-check-entry {
        position: relative;
        
        &:before {
          width: 9px;
          height: 9px;
          background: #4d4d4d;
          border-radius: 50%;
          position: absolute;
          transform: translateX(-50%);
          z-index: 2;
          content: "";
          left: -20px;
          top: 24px;
        }
      }
      .row{
        margin-top: 30px;
        margin-left: 5px;
        i.far.fa-clock{
          font-size: 22px;
        }
        input.form-control {
          color: #000;
        }
        .checkin_location{
          margin-top: 12px;
          display: flex;
          img.icon-locale-detail {
            margin-top: 6px;
            width: 16px;
            height: 16px;
          }
          p.label-checkin_location {
            margin-left: 13px;
            margin-bottom: 0;
            color: #000;
             overflow: hidden;
             text-overflow: ellipsis;
             display: -webkit-box;
             -webkit-line-clamp: 2;
             -webkit-box-orient: vertical;
          }
        }
        .checkout_location{
          margin-top: 12px;
          display: flex;
          img.icon-locale-detail {
            margin-top: 6px;
            width: 16px;
            height: 16px;
          }
          p.label-checkout_location {
            margin-left: 13px;
            color: #000;
             overflow: hidden;
             text-overflow: ellipsis;
             display: -webkit-box;
             -webkit-line-clamp: 2;
             -webkit-box-orient: vertical;
          }
        }
      }
      strong.employee_name {
        color: #088493;
        font-size: 16px;
        margin-left: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      strong.date_timekeeping{
        font-size: 16px;
        color: #000000;
        margin-left: 18px;
      }
      i.fal.fa-calendar-alt,i.far.fa-light.fa-user{
        font-size: 20px;
      }
      .title_timekeeping{
        border-bottom: 1px solid #DDDFE1;
        align-items: center;
        img{
          margin-left:2px;
          margin-bottom:2px
        }
      }
    }
    .modal-footer{
        padding: 0;
        margin: 0 20px !important;
        button.btn.btn-primary{
            font-size: 16px !important;
            padding: 13px 20px;
        }
        .btn-light{
            width: 86px;
            background: #ffffff;
            border: 1px solid #DDDFE1;
            font-size: 16px;
        }

        i.far.fa-calendar-check{
            font-size: 20px;
            margin-right: 10px;
        }
    }
    @media (min-width: 576px) {
      .modal-dialog {
        max-width: 442px;
        margin: 1.75rem auto;
      }
  }
  }