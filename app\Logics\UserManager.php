<?php

namespace App\Logics;

use App\Helpers\ImageHelper;
use App\Helpers\StringHelper;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UserManager
{
    private const USER_DIR = 'users';

    protected $imageHelper;
    protected $stringHelper;

    public function __construct(
        ImageHelper $imageHelper,
        StringHelper $stringHelper
    ) {
        $this->stringHelper = $stringHelper;
        $this->imageHelper = $imageHelper;
    }

    /**
     * Get all users
     * @param $keyword
     * @param $deleted
     * @return User|\Illuminate\Database\Eloquent\Builder
     */
    public function getUserList(Request $request)
    {
        $users = User::select(
            'users.id',
            'users.avatar',
            'users.name',
            'users.email',
            'users.gender',
            'users.phone',
            DB::raw('CONCAT_WS(", ", users.address, wards.name, provinces.name) AS address'),
        )
            ->leftJoin('provinces', 'provinces.province_code', 'users.province_code')
            ->leftJoin('wards', 'wards.ward_code', 'users.ward_code')
            ->orderBy('users.id', 'ASC');

        $stringHelper = new StringHelper();
        if (isset($request->id)) {
            $items = array_filter(array_map("trim", explode(",", $request->id)));
            if (!empty($items)) {
                $users->whereIn('users.id', explode(",", $request->id));
            }
        }
        if (isset($request->keyword)) {
            $keyword = $stringHelper->formatStringWhereLike($request->keyword);
            $users->where('users.name', 'LIKE', '%' . $keyword . '%');
        }
        if (isset($request->email)) {
            $email = $stringHelper->formatStringWhereLike($request->email);
            $users->where('email', 'LIKE', '%' . $email . '%');
        }
        if (isset($request->phone)) {
            $phone = $stringHelper->formatStringWhereLike($request->phone);
            $users->where('phone', 'LIKE', '%' . $phone . '%');
        }
        if (isset($request->gender)) {
            $users->whereIn('gender', $request->gender);
        }
        if ($request->has('deleted')) {
            $users = $users->onlyTrashed();
        }

        return $users;
    }

    /**
     * Get the avatar image for a user.
     *
     * @param int $userId
     * @return \Illuminate\Http\Response
     */
    public function getAvatar($userId)
    {
        // Get user avatar image
        $user = User::withTrashed()
            ->select('avatar')
            ->find($userId);

        // If user not found or avatar is empty, return default image
        if (empty($user) || empty($user->avatar) || !Storage::exists($user->avatar)) {
            $defaultImagePath = '/public/images/user-default.png';
            return response()->file(base_path() . $defaultImagePath);
        }

        // If user found and image is not empty, return the the user's image
        return Storage::response($user->image);
    }

    /**
     * Create a new user.
     */
    public function createUser(Request $request)
    {
        DB::beginTransaction();
        try {
            // Save user to database
            $password = Str::random(16);
            $user =  User::create([
                'name' => $request->name,
                'name_search' => (new StringHelper)->transformSearchFullname($request->name),
                'email' => $request->email,
                'province_code' => $request->prefecture_id,
                'ward_code' => $request->commune_id,
                'address' => $request->address,
                'birthday' => isset($request->birthday) 
                    ? Carbon::createFromFormat(DATE_FORMAT, $request->birthday) : $request->birthday,
                'gender' => $request->gender,
                'phone' => $request->phone,
                'password' => bcrypt($password),
                // 'language_id' => $request->language,
            ]);

            // Store avatar image to storage
            $avatarPath = null;
            if ($request->hasFile('avatar')) {
                $avatarPath = self::USER_DIR . '/' . $user->id . '/' . Str::random(25) . '.jpg';
                $this->imageHelper->resizeImage($request->avatar->getRealPath(), AVATAR_WIDTH, null, $avatarPath);
            }

            $user->update([
                'avatar' => $avatarPath
            ]);

            // Sync roles
            $user->syncRoles($request->role);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update an existing user.
     */
    public function updateUser(Request $request, $userId)
    {
        $user = User::withTrashed()->findOrFail($userId);
        $canEdit = $user->deleted_at == null 
            && auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);

        if (!$canEdit) {
            return false;
        }

        DB::beginTransaction();
        try {
            // Update user to database
            $params = [
                'name' => $request->name,
                'name_search' => (new StringHelper)->transformSearchFullname($request->name),
                'email' => $request->email,
                'province_code' => $request->prefecture_id,
                'ward_code' => $request->commune_id,
                'address' => $request->address,
                'birthday' => isset($request->birthday) 
                    ? Carbon::createFromFormat(DATE_FORMAT, $request->birthday) : $request->birthday,
                'gender' => $request->gender,
                'phone' => $request->phone,
                // 'language_id' => $request->language,
            ];
            if ($request->has('password') && !empty($request->password)) {
                $params['password'] = bcrypt($request->password);
            }

            // Store avatar image to storage
            $avatarPath = null;
            if ($request->hasFile('avatar')) {
                $avatarPath = self::USER_DIR . '/' . $user->id . '/' . Str::random(25) . '.jpg';
                $this->imageHelper->resizeImage($request->avatar->getRealPath(), AVATAR_WIDTH, null, $avatarPath);
                $params['avatar'] = $avatarPath;
            }
            // Update user
            $user->update($params);

            // Sync roles
            $user->syncRoles($request->role);

            DB::commit();

            // Delete old avatar if exists
            $oldAvatarPath = $user->avatar;
            if ($oldAvatarPath && Storage::exists($oldAvatarPath)) {
                Storage::delete($oldAvatarPath);
            }

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function deleteUser($userId)
    {
        $user = User::withTrashed()->findOrFail($userId);
        $canDelete = auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);

        if (!$canDelete) {
            return false;
        }

        DB::beginTransaction();
        try {
            // Delete user
            $user->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
