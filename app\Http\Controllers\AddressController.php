<?php

namespace App\Http\Controllers;

use App\Models\Ward;
use Illuminate\Http\Request;


class AddressController extends Controller
{
    /**
     * Get the list of communes based on the selected province.
     *
     * @param Request $request
     * @return array
     */
    public function getCommuneList(Request $request) {
        $provinceCode = $request->id;
        $communes = Ward::select('ward_code', 'name')
            ->where('province_code', $provinceCode)
            ->orderBy('id')
            ->get();

        $options = [];
        foreach ($communes as $commune) {
            $options[] = [
                'value' => $commune->ward_code,
                'text' => $commune->name
            ];
        }

        return [
            'status' => 200,
            'options' => $options
        ];
    }
}
