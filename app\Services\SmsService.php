<?php

namespace App\Services;


use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    // Contract types
    const CONTRACT_TYPE_CSKH = "1";
    const CONTRACT_TYPE_QC = "2";

    // Data coding
    const DATA_CODING_NO_DIACRITICS = "0";
    const DATA_CODING_VIETNAMESE = "8";

    // Fixed values
    const IS_TELCO_SUB = "0";
    const SMS_METHOD_NAME = "send_sms_list";

    // Config properties
    private string $apiUrl;
    private string $labelId;
    private string $contractId;
    private string $agentId;
    private string $apiUser;
    private string $apiPass;
    private string $username;
    private string $otpTemplateId;

    public function __construct()
    {
        $this->apiUrl = config('services.sms.url');
        $this->labelId = config('services.sms.label_id');
        $this->contractId = config('services.sms.contract_id');
        $this->agentId = config('services.sms.agent_id');
        $this->apiUser = config('services.sms.api_user');
        $this->apiPass = config('services.sms.api_pass');
        $this->username = config('services.sms.username');
        $this->otpTemplateId = config('services.sms.otp_template_id');
    }

    /**
     * Send OTP SMS
     */
    public function sendOtp(string $otp, array $phones, array $options = []): array
    {
        return $this->send(
            templateId: $this->otpTemplateId,
            params: [
                [
                    "NUM" => "1",
                    "CONTENT" => $otp
                ]
            ],
            phones: $phones,
            reqIdPrefix: 'OTP',
            code: $otp,
            options: $options
        );
    }

    /**
     * Core send method - build full payload and execute
     */
    private function send(string $templateId, array $params, array $phones, string $reqIdPrefix = 'SMS', string $code = '', array $options = []): array
    {
        // Build full payload
        $payload = [
            "RQST" => [
                "name" => self::SMS_METHOD_NAME,
                "REQID" => (string) $this->generateRequestId($reqIdPrefix, $code),
                "LABELID" => (string)$this->labelId,
                "CONTRACTTYPEID" => (string)($options['contractType'] ?? self::CONTRACT_TYPE_CSKH),
                "CONTRACTID" => (string)$this->contractId,
                "TEMPLATEID" => (string)$templateId,
                "PARAMS" => $params,
                'SCHEDULETIME' => $options['scheduleTime'] ?? '',
                'MOBILELIST' => $this->convertPhonesToString($phones),
                'ISTELCOSUB' => (string)self::IS_TELCO_SUB,
                'AGENTID' => (string) $this->agentId,
                'APIUSER' => $this->apiUser,
                'APIPASS' => $this->apiPass,
                'USERNAME' => $this->username,
                'DATACODING' => (string)($options['dataCoding'] ?? self::DATA_CODING_NO_DIACRITICS),
            ]
        ];

        // Add optional fields if provided
        if (!empty($options['saleOrderId'])) {
            $payload['RQST']['SALEORDERID'] = $options['saleOrderId'];
        }

        if (!empty($options['packageId'])) {
            $payload['RQST']['PACKAGEID'] = $options['packageId'];
        }

        // Log request
        Log::info('SMS Request', [
            'request_id' => $payload['RQST']['REQID'],
            'phone_count' => count($phones),
            'template_id' => $templateId
        ]);

        Log::debug('SMS API Payload', [
            'json' => json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT),
            'payload' => $payload
        ]);

        $response = $this->makeApiCall($payload);
        $errorCode = (int)$response['RPLY']['ERROR'];

        // Log error only when code != 0
        if ($errorCode !== 0) {
            Log::error('SMS API Error', [
                'request_id' => $payload['RQST']['REQID'],
                'error_code' => $errorCode,
                'error_desc' => $response['RPLY']['ERROR_DESC'],
                'response' => $response
            ]);
        }
        return $response;
    }

    /**
     * Make API call
     */
    private function makeApiCall(array $payload): array
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json; charset=UTF-8',
        ])->post($this->apiUrl, $payload);

        // Log response for debugging
        Log::debug('SMS API Response', [
            'status' => $response->status(),
            'body' => $response->body()
        ]);
        return $response->json();
    }

    /**
     * Convert phone array to comma-separated string
     */
    private function convertPhonesToString(array $phones): string
    {
        // Remove empty values and convert to string
        $phones = array_filter($phones);
        return implode(',', $phones);
    }

    /**
     * Generate unique request ID with datetime and code
     */
    private function generateRequestId(string $prefix = 'SMS', string $code = ''): string
    {
        return $prefix . '_' . date('YmdHis') . '_' . $code;
    }
}
