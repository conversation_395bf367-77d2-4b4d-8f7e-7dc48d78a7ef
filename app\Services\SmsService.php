<?php

namespace App\Services;

use App\Enums\SmsErrorEnum;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    // Contract types
    const CONTRACT_TYPE_CSKH = 1;
    const CONTRACT_TYPE_QC = 2;

    // Data coding
    const DATA_CODING_NO_DIACRITICS = 0;
    const DATA_CODING_VIETNAMESE = 8;

    // Fixed values
    const IS_TELCO_SUB = 0;
    const SMS_METHOD_NAME = "send_sms_list";

    // Config properties
    private string $apiUrl;
    private string $labelId;
    private string $contractId;
    private string $agentId;
    private string $apiUser;
    private string $apiPass;
    private string $username;
    private string $otpTemplateId;

    public function __construct()
    {
        $this->apiUrl = config('services.sms.url');
        $this->labelId = config('services.sms.label_id');
        $this->contractId = config('services.sms.contract_id');
        $this->agentId = config('services.sms.agent_id');
        $this->apiUser = config('services.sms.api_user');
        $this->apiPass = config('services.sms.api_pass');
        $this->username = config('services.sms.username');
        $this->otpTemplateId = config('services.sms.otp_template_id');
    }

    /**
     * Send OTP SMS
     */
    public function sendOtp(string $otp, array $phones, array $options = []): array
    {
        return $this->send(
            templateId: $this->otpTemplateId,
            params: [
                [
                    "NUM" => "1",
                    "CONTENT" => $otp
                ]
            ],
            phones: $phones,
            reqIdPrefix: 'OTP',
            code: $otp,
            options: $options
        );
    }

    /**
     * Core send method - build full payload and execute
     */
    private function send(string $templateId, array $params, array $phones, string $reqIdPrefix = 'SMS', string $code = '', array $options = []): array
    {
        // Build full payload
        $payload = [
            "RQST" => [
                "name" => self::SMS_METHOD_NAME,
                "REQID" => $this->generateRequestId($reqIdPrefix, $code),
                "LABELID" => (string)$this->labelId,
                "CONTRACTTYPEID" => $options['contractType'] ?? self::CONTRACT_TYPE_CSKH,
                "CONTRACTID" => (string)$this->contractId,
                "TEMPLATEID" => (string)$templateId,
                "PARAMS" => $params,
                'SCHEDULETIME' => $options['scheduleTime'] ?? '',
                'MOBILELIST' => $this->convertPhonesToString($phones),
                'ISTELCOSUB' => self::IS_TELCO_SUB,
                'AGENTID' => (string) $this->agentId,
                'APIUSER' => $this->apiUser,
                'APIPASS' => $this->apiPass,
                'USERNAME' => $this->username,
                'DATACODING' => (string)($options['dataCoding'] ?? self::DATA_CODING_NO_DIACRITICS),
            ]
        ];

        // Add optional fields if provided
        if (!empty($options['saleOrderId'])) {
            $payload['RQST']['SALEORDERID'] = $options['saleOrderId'];
        }

        if (!empty($options['packageId'])) {
            $payload['RQST']['PACKAGEID'] = $options['packageId'];
        }

        // Log request
        Log::info('SMS Request', [
            'request_id' => $payload['RQST']['REQID'],
            'phone_count' => count($phones),
            'template_id' => $templateId
        ]);

        Log::debug('SMS API Payload', [
            'json' => json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT),
            'payload' => $payload
        ]);

        // Debug: Compare with curl payload
        $this->logPayloadComparison($payload);

        $response = $this->makeApiCall($payload);
        $errorCode = (int)$response['RPLY']['ERROR'];

        // Enhance response with detailed description
        $response['detailed_description'] = SmsErrorEnum::getDescriptionByCode($errorCode);

        // Log error only when code != 0
        if ($errorCode !== 0) {
            Log::error('SMS API Error', [
                'request_id' => $payload['RQST']['REQID'],
                'error_code' => $errorCode,
                'error_desc' => $response['RPLY']['ERROR_DESC'],
                'response' => $response
            ]);
        }
        return $response;
    }

    /**
     * Make API call
     */
    private function makeApiCall(array $payload): array
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json; charset=UTF-8',
                'Cookie' => 'JSESSIONID=' . $this->generateSessionId(),
            ])->post($this->apiUrl, $payload);

            // Log response for debugging
            Log::debug('SMS API Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            // Check if response is successful
            if (!$response->successful()) {
                Log::error('SMS API HTTP Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return [
                    'RPLY' => [
                        'ERROR' => -1,
                        'ERROR_DESC' => 'HTTP Error: ' . $response->status()
                    ]
                ];
            }

            $responseData = $response->json();

            // Validate response structure
            if (!isset($responseData['RPLY']) || !isset($responseData['RPLY']['ERROR'])) {
                Log::error('SMS API Invalid Response Structure', [
                    'response' => $responseData
                ]);
                return [
                    'RPLY' => [
                        'ERROR' => -1,
                        'ERROR_DESC' => 'Invalid response structure'
                    ]
                ];
            }

            return $responseData;

        } catch (\Exception $e) {
            Log::error('SMS API Exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'RPLY' => [
                    'ERROR' => -1,
                    'ERROR_DESC' => 'Exception: ' . $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Convert phone array to comma-separated string
     */
    private function convertPhonesToString(array $phones): string
    {
        // Remove empty values and convert to string
        $phones = array_filter($phones);
        return implode(',', $phones);
    }

    /**
     * Generate unique request ID with datetime and code
     */
    private function generateRequestId(string $prefix = 'SMS', string $code = ''): string
    {
        return $prefix . '_' . date('YmdHis') . '_' . $code;
    }

    /**
     * Generate session ID for JSESSIONID cookie
     */
    private function generateSessionId(): string
    {
        return strtoupper(bin2hex(random_bytes(16)));
    }

    /**
     * Log payload comparison with curl example for debugging
     */
    private function logPayloadComparison(array $payload): void
    {
        $curlExample = [
            "RQST" => [
                "name" => "send_sms_list",
                "REQID" => "OTP_20250828154556_123456",
                "LABELID" => "10207847",
                "CONTRACTTYPEID" => "1",
                "CONTRACTID" => "10016178",
                "TEMPLATEID" => "114050792",
                "PARAMS" => [
                    [
                        "NUM" => "1",
                        "CONTENT" => "123456"
                    ]
                ],
                "SCHEDULETIME" => "",
                "MOBILELIST" => "84394908085",
                "ISTELCOSUB" => "0",
                "AGENTID" => "244",
                "APIUSER" => "BEEID",
                "APIPASS" => "Abc123456@",
                "USERNAME" => "BEEID",
                "DATACODING" => "0"
            ]
        ];

        Log::debug('SMS Payload Comparison', [
            'current_payload' => $payload,
            'curl_example' => $curlExample,
            'differences' => $this->findPayloadDifferences($payload['RQST'], $curlExample['RQST'])
        ]);
    }

    /**
     * Find differences between current payload and curl example
     */
    private function findPayloadDifferences(array $current, array $example): array
    {
        $differences = [];

        foreach ($example as $key => $value) {
            if (!isset($current[$key])) {
                $differences["missing_$key"] = "Key '$key' missing in current payload";
            } elseif ($current[$key] !== $value && $key !== 'REQID') { // Skip REQID as it's always different
                $differences["different_$key"] = [
                    'current' => $current[$key],
                    'expected' => $value
                ];
            }
        }

        return $differences;
    }
}
