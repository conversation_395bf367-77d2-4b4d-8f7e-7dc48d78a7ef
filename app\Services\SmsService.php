<?php

namespace App\Services;

use App\Enums\SmsErrorEnum;
use App\Jobs\SendSmsJob;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    // Contract types
    const CONTRACT_TYPE_CSKH = "1";
    const CONTRACT_TYPE_QC = "2";

    // Data coding
    const DATA_CODING_NO_DIACRITICS = "0";
    const DATA_CODING_VIETNAMESE = "8";

    // Fixed values
    const IS_TELCO_SUB = "0";
    const SMS_METHOD_NAME = "send_sms_list";

    // Cache prefix
    const OTP_CACHE_PREFIX = 'otp_attempts';

    // Config properties
    private string $apiUrl;
    private string $labelId;
    private string $contractId;
    private string $agentId;
    private string $apiUser;
    private string $apiPass;
    private string $username;
    private string $otpTemplateId;

    public function __construct()
    {
        $this->apiUrl = config('services.sms.url');
        $this->labelId = config('services.sms.label_id');
        $this->contractId = config('services.sms.contract_id');
        $this->agentId = config('services.sms.agent_id');
        $this->apiUser = config('services.sms.api_user');
        $this->apiPass = config('services.sms.api_pass');
        $this->username = config('services.sms.username');
        $this->otpTemplateId = config('services.sms.otp_template_id');

        $this->validateConfig();
    }

    /**
     * Validate SMS configuration
     */
    private function validateConfig(): void
    {
        $requiredConfigs = [
            'apiUrl' => 'SMS API URL',
            'labelId' => 'SMS Label ID',
            'contractId' => 'SMS Contract ID',
            'agentId' => 'SMS Agent ID',
            'apiUser' => 'SMS API User',
            'apiPass' => 'SMS API Password',
            'username' => 'SMS Username',
            'otpTemplateId' => 'SMS OTP Template ID'
        ];

        foreach ($requiredConfigs as $property => $description) {
            if (empty($this->$property)) {
                throw new \InvalidArgumentException("$description is not configured or empty");
            }
        }
    }

    /**
     * Send OTP SMS (synchronous) with rate limiting
     */
    public function sendOtp(string $otp, array $phones, array $options = []): array
    {
        // Validate OTP attempts for each phone
        foreach ($phones as $phone) {
            if (!$this->canSendOtp($phone)) {
                throw new \Exception("OTP limit exceeded for phone: $phone");
            }
        }

        $response = $this->send(
            templateId: $this->otpTemplateId,
            params: [
                [
                    "NUM" => "1",
                    "CONTENT" => $otp
                ]
            ],
            phones: $phones,
            reqIdPrefix: 'OTP',
            code: $otp,
            options: $options
        );

        // Track OTP attempts only if SMS was sent successfully
        $errorCode = (int)($response['RPLY']['ERROR'] ?? -1);
        if ($errorCode === 0) {
            foreach ($phones as $phone) {
                $this->trackOtpAttempt($phone);
            }
        }

        return $response;
    }

    /**
     * Send OTP SMS via Job (asynchronous)
     */
    public function sendOtpAsync(string $otp, array $phones, array $options = []): void
    {
        $this->sendAsync(
            templateId: $this->otpTemplateId,
            params: [
                [
                    "NUM" => "1",
                    "CONTENT" => $otp
                ]
            ],
            phones: $phones,
            reqIdPrefix: 'OTP',
            code: $otp,
            options: $options
        );
    }

    /**
     * Send SMS via Job (asynchronous)
     */
    public function sendAsync(
        string $templateId,
        array $params,
        array $phones,
        string $reqIdPrefix = 'SMS',
        string $code = '',
        array $options = []
    ): void {
        SendSmsJob::dispatch(
            $templateId,
            $params,
            $phones,
            $reqIdPrefix,
            $code,
            $options
        );

        Log::info('SMS Job Dispatched', [
            'phone_count' => count($phones),
            'template_id' => $templateId,
            'req_prefix' => $reqIdPrefix
        ]);
    }

    /**
     * Core send method - build full payload and execute
     */
    public function send(string $templateId, array $params, array $phones, string $reqIdPrefix = 'SMS', string $code = '', array $options = []): array
    {
        // Build full payload
        $payload = [
            "RQST" => [
                "name" => self::SMS_METHOD_NAME,
                "REQID" => (string) $this->generateRequestId($reqIdPrefix, $code),
                "LABELID" => (string)$this->labelId,
                "CONTRACTTYPEID" => (string)($options['contractType'] ?? self::CONTRACT_TYPE_CSKH),
                "CONTRACTID" => (string)$this->contractId,
                "TEMPLATEID" => (string)$templateId,
                "PARAMS" => $params,
                'SCHEDULETIME' => $options['scheduleTime'] ?? '',
                'MOBILELIST' => $this->convertPhonesToString($phones),
                'ISTELCOSUB' => (string)self::IS_TELCO_SUB,
                'AGENTID' => (string) $this->agentId,
                'APIUSER' => $this->apiUser,
                'APIPASS' => $this->apiPass,
                'USERNAME' => $this->username,
                'DATACODING' => (string)($options['dataCoding'] ?? self::DATA_CODING_NO_DIACRITICS),
            ]
        ];

        // Add optional fields if provided
        if (!empty($options['saleOrderId'])) {
            $payload['RQST']['SALEORDERID'] = $options['saleOrderId'];
        }

        if (!empty($options['packageId'])) {
            $payload['RQST']['PACKAGEID'] = $options['packageId'];
        }

        // Log request
        Log::info('SMS Request', [
            'request_id' => $payload['RQST']['REQID'],
            'phone_count' => count($phones),
            'template_id' => $templateId
        ]);

        $response = $this->makeApiCall($payload);
        $errorCode = (int)$response['RPLY']['ERROR'];

        // Log error only when code != 0
        if ($errorCode !== 0) {
            Log::error('SMS API Error', [
                'request_id' => $payload['RQST']['REQID'],
                'error_code' => $errorCode,
                'error_desc' => $response['RPLY']['ERROR_DESC'],
                'response' => $response
            ]);
        }
        return $response;
    }

    /**
     * Make API call
     */
    private function makeApiCall(array $payload): array
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json; charset=UTF-8',
        ])->post($this->apiUrl, $payload);

        return $response->json();
    }

    /**
     * Convert phone array to comma-separated string
     */
    private function convertPhonesToString(array $phones): string
    {
        // Remove empty values and convert to string
        $phones = array_filter($phones);
        return implode(',', $phones);
    }

    /**
     * Generate unique request ID with datetime and code
     */
    private function generateRequestId(string $prefix = 'SMS', string $code = ''): string
    {
        return $prefix . '_' . date('YmdHis') . '_' . $code;
    }

    /**
     * Check if OTP can be sent to phone number
     */
    private function canSendOtp(string $phone): bool
    {
        $dailyCount = $this->getOtpAttemptCount($phone, 'daily');
        $hourlyCount = $this->getOtpAttemptCount($phone, 'hourly');

        $dailyLimit = config('sms.rate_limits.otp.max_attempts_per_phone_per_day', 5);
        $hourlyLimit = config('sms.rate_limits.otp.max_attempts_per_phone_per_hour', 3);

        return $dailyCount < $dailyLimit && $hourlyCount < $hourlyLimit;
    }

    /**
     * Track OTP attempt for phone number
     */
    private function trackOtpAttempt(string $phone): void
    {
        $dailyKey = $this->getOtpCacheKey($phone, 'daily');
        $hourlyKey = $this->getOtpCacheKey($phone, 'hourly');

        // Increment daily counter (expires at end of day)
        $dailyCount = Cache::get($dailyKey, 0) + 1;
        $dailyExpiry = now()->endOfDay();
        Cache::put($dailyKey, $dailyCount, $dailyExpiry);

        // Increment hourly counter (expires after 1 hour)
        $hourlyCount = Cache::get($hourlyKey, 0) + 1;
        Cache::put($hourlyKey, $hourlyCount, 3600); // 1 hour in seconds

        Log::info('OTP Attempt Tracked', [
            'phone' => $phone,
            'daily_count' => $dailyCount,
            'hourly_count' => $hourlyCount
        ]);
    }

    /**
     * Get OTP attempt count for phone number
     */
    private function getOtpAttemptCount(string $phone, string $period): int
    {
        $cacheKey = $this->getOtpCacheKey($phone, $period);
        return Cache::get($cacheKey, 0);
    }

    /**
     * Generate cache key for OTP attempts
     */
    private function getOtpCacheKey(string $phone, string $period): string
    {
        $date = now();
        $suffix = match($period) {
            'daily' => $date->format('Y-m-d'),
            'hourly' => $date->format('Y-m-d-H'),
            default => $date->format('Y-m-d')
        };

        return self::OTP_CACHE_PREFIX . ':' . $phone . ':' . $suffix;
    }

    /**
     * Get OTP attempt info for phone number (for debugging)
     */
    public function getOtpAttemptInfo(string $phone): array
    {
        return [
            'phone' => $phone,
            'daily_count' => $this->getOtpAttemptCount($phone, 'daily'),
            'hourly_count' => $this->getOtpAttemptCount($phone, 'hourly'),
            'daily_limit' => config('sms.rate_limits.otp.max_attempts_per_phone_per_day', 5),
            'hourly_limit' => config('sms.rate_limits.otp.max_attempts_per_phone_per_hour', 3),
            'can_send' => $this->canSendOtp($phone)
        ];
    }
}
