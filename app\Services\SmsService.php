<?php

namespace App\Services;

use App\Enums\SmsErrorEnum;
use App\Jobs\SendSmsJob;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    // Contract types
    const CONTRACT_TYPE_CSKH = "1";
    const CONTRACT_TYPE_QC = "2";

    // Data coding
    const DATA_CODING_NO_DIACRITICS = "0";
    const DATA_CODING_VIETNAMESE = "8";

    // Fixed values
    const IS_TELCO_SUB = "0";
    const SMS_METHOD_NAME = "send_sms_list";

    // Config properties
    private string $apiUrl;
    private string $labelId;
    private string $contractId;
    private string $agentId;
    private string $apiUser;
    private string $apiPass;
    private string $username;
    private string $otpTemplateId;

    public function __construct()
    {
        $this->apiUrl = config('services.sms.url');
        $this->labelId = config('services.sms.label_id');
        $this->contractId = config('services.sms.contract_id');
        $this->agentId = config('services.sms.agent_id');
        $this->apiUser = config('services.sms.api_user');
        $this->apiPass = config('services.sms.api_pass');
        $this->username = config('services.sms.username');
        $this->otpTemplateId = config('services.sms.otp_template_id');

        $this->validateConfig();
    }

    /**
     * Validate SMS configuration
     */
    private function validateConfig(): void
    {
        $requiredConfigs = [
            'apiUrl' => 'SMS API URL',
            'labelId' => 'SMS Label ID',
            'contractId' => 'SMS Contract ID',
            'agentId' => 'SMS Agent ID',
            'apiUser' => 'SMS API User',
            'apiPass' => 'SMS API Password',
            'username' => 'SMS Username',
            'otpTemplateId' => 'SMS OTP Template ID'
        ];

        foreach ($requiredConfigs as $property => $description) {
            if (empty($this->$property)) {
                throw new \InvalidArgumentException("$description is not configured or empty");
            }
        }
    }

    /**
     * Send OTP SMS via Job
     */
    public function sendOtp(string $otp, array $phones, array $options = []): void
    {
        SendSmsJob::dispatch(
            $this->otpTemplateId,
            [
                [
                    "NUM" => "1",
                    "CONTENT" => $otp
                ]
            ],
            $phones,
            'OTP',
            $otp,
            $options
        );

        Log::info('OTP SMS Job Dispatched', [
            'phone_count' => count($phones),
            'otp' => $otp
        ]);
    }

    /**
     * Send SMS via Job
     */
    public function sendAsync(
        string $templateId,
        array $params,
        array $phones,
        string $reqIdPrefix = 'SMS',
        string $code = '',
        array $options = []
    ): void {
        SendSmsJob::dispatch(
            $templateId,
            $params,
            $phones,
            $reqIdPrefix,
            $code,
            $options
        );

        Log::info('SMS Job Dispatched', [
            'phone_count' => count($phones),
            'template_id' => $templateId,
            'req_prefix' => $reqIdPrefix
        ]);
    }





    /**
     * Generate unique request ID with datetime and code
     */
    public function generateRequestId(string $prefix = 'SMS', string $code = ''): string
    {
        return $prefix . '_' . date('YmdHis') . '_' . $code;
    }

    /**
     * Convert phone array to comma-separated string
     */
    public function convertPhonesToString(array $phones): string
    {
        // Remove empty values and convert to string
        $phones = array_filter($phones);
        return implode(',', $phones);
    }

    /**
     * Make API call - public for Job access
     */
    public function makeApiCall(array $payload): array
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json; charset=UTF-8',
        ])->post($this->apiUrl, $payload);

        // Log response for debugging
        Log::debug('SMS API Response', [
            'status' => $response->status(),
            'body' => $response->body()
        ]);
        return $response->json();
    }

    // Getter methods for Job access
    public function getLabelId(): string { return $this->labelId; }
    public function getContractId(): string { return $this->contractId; }
    public function getAgentId(): string { return $this->agentId; }
    public function getApiUser(): string { return $this->apiUser; }
    public function getApiPass(): string { return $this->apiPass; }
    public function getUsername(): string { return $this->username; }
}
