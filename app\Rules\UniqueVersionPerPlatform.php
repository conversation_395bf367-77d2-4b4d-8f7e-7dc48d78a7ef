<?php

namespace App\Rules;

use App\Models\AppVersion;
use Illuminate\Contracts\Validation\Rule;

class UniqueVersionPerPlatform implements Rule
{
    private $platforms;
    private $versionId;
    private $duplicatePlatforms = [];
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($platforms, $versionId = null)
    {
        $this->platforms = is_array($platforms) ? $platforms : [$platforms];
        $this->versionId = $versionId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $query = AppVersion::where('version', $value)
            ->whereIn('platform', $this->platforms);

        if ($this->versionId) {
            $query->where('id', '!=', $this->versionId);
        }

        $this->duplicatePlatforms = $query->pluck('platform')->toArray();

        return empty($this->duplicatePlatforms);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        $platformNames = collect($this->duplicatePlatforms)->map(function ($platform) {
            return $platform == AppVersion::IOS
                ? __('language.ios')
                : __('language.android');
        })->toArray();

        if (count($platformNames) === 1) {
            return __('validation.custom.version_duplicate_platform', ['platform' => $platformNames[0]]);
        }

        return __('validation.custom.version_duplicate_platforms', ['platforms' => implode(', ', $platformNames)]);
    }
}
