<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\OrderRequest;
use App\Logics\OrderManager;

class OrderController extends Controller
{
    protected $orderManager;

    public function __construct(OrderManager $orderManager)
    {
        $this->orderManager = $orderManager;
    }

    /**
     * Store a newly created order in storage.
     */
    public function store(OrderRequest $request)
    {
        $user = $request->user();
        $data = $request->only(['type', 'amount', 'currency', 'meta']);
        $data['user_id'] = $user->id;

        $order = $this->orderManager->create($data);

        return response()->json([
            'message' => __('message.success'),
            'data' => [
                'order' => $order
            ]
        ]);
    }
}