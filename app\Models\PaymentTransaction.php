<?php

namespace App\Models;

use App\Enums\TransactionStatus;
use Illuminate\Database\Eloquent\Model;

class PaymentTransaction extends Model
{
    protected $fillable = [
        'payment_id','event','status','provider_code','provider_message',
        'signature_valid','request_payload','response_payload'
    ];

    protected $casts = [
        'status'           => TransactionStatus::class,
        'signature_valid'  => 'boolean',
        'request_payload'  => 'array',
        'response_payload' => 'array',
    ];

    public function payment() {
        return $this->belongsTo(Payment::class);
    }
}
