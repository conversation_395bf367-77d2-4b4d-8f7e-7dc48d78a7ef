<?php

namespace App\Http\Requests;

use App\Models\AppVersion;
use Illuminate\Foundation\Http\FormRequest;

class CheckVersionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'device' => ['required', 'integer', 'in:' . AppVersion::IOS . ',' . AppVersion::ANDROID],
            'version' => ['required', 'string', 'regex:/^\d+(?:\.\d+)*$/', 'max:20'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'device.required' => __('validation.required', ['attribute' => __('validation.attributes.device_type')]),
            'device.integer' => __('validation.integer', ['attribute' => __('validation.attributes.device_type')]),
            'device.in' => __('validation.in', ['attribute' => __('validation.attributes.device_type')]),
            'version.required' => __('validation.required', ['attribute' => __('validation.attributes.version_name')]),
            'version.regex' => __('validation.regex', ['attribute' => __('validation.attributes.version_name')]),
            'version.max' => __('validation.max.string', ['attribute' => __('validation.attributes.version_name'), 'max' => 20])
        ];
    }
}
