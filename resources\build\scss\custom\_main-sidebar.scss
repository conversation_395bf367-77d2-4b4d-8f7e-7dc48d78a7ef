.nav.nav-pills.nav-sidebar.flex-column.nav-legacy.nav-flat.nav-treeview-addon{
    .nav-item{
        .nav-link{
           div{
                text-overflow: ellipsis;
                overflow: hidden;
           }
            .task-name{
                display: block; 
                display: -webkit-box;
                -webkit-line-clamp: 2; 
                -webkit-box-orient: vertical;
                white-space: normal;
                max-height: 3.0em;
            }
            .user{
                font-size: 10px;
            }

          .project-name{
            font-size: 10px;
          }
        }
    }
}
.nav-sidebar {
    .nav-item {
        background: #088493;

        &.menu-open {
            //background: #0791A3 0% 0% no-repeat padding-box;
            //box-shadow: 0px 1px 14px #00000057;
            opacity: 1;
            
            &>.nav-link {
                background-color: #088493!important;
            }
        }
        
        .nav-treeview {
           background-color: #0791A3;

            .nav-item {
                background-color: #0791A3;
            }
        }

        .icon-nav__item {
            max-width: 17px;
        }
    }
	a {
		font-size: 16px;
	}
}

.nav-sidebar .nav-link p {
    color: #fff;
    font-weight: 300;
}

.nav-sidebar > .nav-item .nav-icon {
    color: #fff;
}
.sidebar {
    z-index: 2;
}
.aside-pushmenu {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #292828 0% 0% no-repeat padding-box;
    padding: 6px 0;
    z-index: 2;
    height: 49px;
    a {
        color: #fff;
    }
}
//body.sidebar-mini.layout-fixed.sidebar-collapse {
//    aside.main-sidebar.sidebar-dark-bee.elevation-4 {
//        margin-left: 0;
//        width: 3rem;
//    }
//    .brand-link {
//        width: 3rem;
//    }
//}
//.sidebar-mini.sidebar-collapse.layout-fixed .main-sidebar:hover .brand-link {
//    width: 3rem;
//}
.aside-pushmenu a:hover {
    color: #088493;
}
body.sidebar-mini.layout-fixed.sidebar-closed.sidebar-collapse {
    aside.main-sidebar.sidebar-dark-bee.elevation-4 { 
        
        
    }
}
body.sidebar-collapse {
    .sidebar-mini.sidebar-collapse .sidebar .user-panel > .info, 
    .sidebar-mini.sidebar-collapse .nav-sidebar .nav-link p, 
    .sidebar-mini.sidebar-collapse .brand-text {
        animation-name: inherit;
        visibility: inherit;
    }
    .nav-sidebar .nav-link > .right:nth-child(2), 
    .nav-sidebar .nav-link > p > .right:nth-child(2) {
        right: 16px;
        bottom: inherit;
        top: 2px;
        visibility: inherit;
    }


    .nav-sidebar .nav-link > .right, .nav-sidebar .nav-link > p > .right {
        right: 16px;
        bottom: inherit;
        top: 2px;
    }
}

@media(max-width: 991px) {
    nav.main-header {
        background: #088493;
    }
    .main-header {
        padding-right: 20px;
        padding-left: 20px;
    }
    .main-header.navbar .navbar-nav a.nav-link {
        color: #fff;
    }
    .main-header .navbar-nav:first-child li:first-child {
        display: block;
    }
    .main-header .navbar-nav:first-child li:not(:first-child) {
        display: none;
    }
    //.sidebar-mini.sidebar-collapse .content-wrapper,
    //.sidebar-mini.sidebar-collapse .main-footer,
    //.sidebar-mini.sidebar-collapse .main-header {
    //    margin-left: 0!important;
    //    padding-left: 0;
    //}
    //body.sidebar-mini.layout-fixed.sidebar-collapse aside.main-sidebar.sidebar-dark-bee.elevation-4 {
    //    margin-left: -320px;
    //}
}
@media (max-width: 992px) {
    //.sidebar-mini.sidebar-collapse .content-wrapper,
    //.sidebar-mini.sidebar-collapse .main-footer,
    //.sidebar-mini.sidebar-collapse .main-header {
    //    margin-left: 0!important;
    //    padding-left: 0;
    //}
}

body.sidebar-collapse .nav-sidebar .nav-link > i.right, 
body.sidebar-collapse .nav-sidebar .nav-link > p > i.right {
    display: none;
}

body.sidebar-collapse {
    .aside-pushmenu {
        .nav-link i:before {
            content: "\f105";
        }
    }
}
.layout-fixed.sidebar-collapse {
    .brand-link {
        width: auto;
    }
}

//.sidebar-mini {
//    //margin-left: -25px;
//    
//    .arrow {
//        display: none;
//    }
//    
//    .tooltip-inner {
//        background: #FFFFFF 0% 0% no-repeat padding-box;
//        box-shadow: 0px 3px 6px #00000029;
//        border-radius: 5px;
//        color: #0791A3;
//        font-size: 14px;
//        max-width: inherit;
//        display: inline-flex;
//        width: auto;
//        white-space: normal;
//        
//        span.badge {
//            margin-left: 5px;
//            background: #FF0000;
//            border-radius: 60px;
//            font-size: 14px;
//            line-height: 1;
//        }
//    }
//}
.tooltip_templates {
    display: none;
}
.sidebar-mini {
    
    .tooltipster-sidetip {
        display: none;
    }
    
    &.sidebar-collapse {
        .tooltipster-sidetip {
            display: flex;
            margin-left: -35px;
        }
    }
    
    .tooltipster-sidetip .tooltipster-box {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        box-shadow: 0px 3px 6px #00000029;
        border-radius: 5px;
        color: #0791A3;
        font-size: 14px;
        max-width: inherit;
        display: inline-flex;
        width: auto;
        white-space: normal;
        border: 0;

        span.badge {
            margin-left: 5px;
            background: #FF0000;
            border-radius: 60px;
            font-size: 14px;
            line-height: 1;
            padding: 2px 8px 3px 8px;
            font-weight: normal;
        }
    }
    .tooltipster-sidetip .tooltipster-content {
        color: inherit;
    }
}


.sidebar-mini .tooltip-custom {
    display: none;
}
.sidebar-mini.sidebar-collapse .tooltip-custom {
    display: block;
}
.sidebar-mini.sidebar-collapse {
    .nav-sidebar .nav-item > .nav-link .right.badge {
        text-indent: -999px;
        width: 12px;
        height: 12px;
        padding: 0px;
        display: block;
    }
}
.main-sidebar .nav-sidebar {
    padding-bottom: 32px;
}
.main-sidebar {
    .badge-danger {
        background-color: #FF0000;
    }
}

@media (min-width: 992px) {
    body.sidebar-collapse.sidebar-mini.sidebar-collapse .nav-sidebar .nav-link p {
        animation-name: inherit!important;
        visibility: inherit;
    }
}
@media (min-width: 992px) {
    .sidebar-mini.sidebar-collapse .main-sidebar:hover .brand-image, 
    .sidebar-mini.sidebar-collapse .main-sidebar.sidebar-focused .brand-image {
        margin-right: 0;
    }
}

@media (max-width: 767px) {
    .main-header {
        padding-right: 12px;
        padding-left: 12px;
    }
}