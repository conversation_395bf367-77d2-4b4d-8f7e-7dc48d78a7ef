<?php

namespace App\Http\Middleware;

use Closure;

class Session
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $currentPath = $request->getPathInfo();
        if (!empty($request->all())) {
            if (isset($request->refresh)) {
                session()->forget($currentPath);
                request()->query->remove('refresh');
            } else {
                if ($request->isMethod('GET')) {
                    session([$currentPath => $request->all()]);
                }
            }
        } else {
            if (session()->exists($currentPath)) {
                $request->query->add(session($currentPath));
            }
        }
        return $next($request);
    }
}
