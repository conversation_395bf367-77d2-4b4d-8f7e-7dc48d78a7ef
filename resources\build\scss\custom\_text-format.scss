.text-format{
  line-height: 1.5;
  *{
    max-width: 100%;
  }
  h1,h2,h3,h4,h5,h6{
    color: #333;
    font-weight: bold;
    margin-bottom: 10px;
    &:not(:first-child){
      padding-top: 10px;
    }
  }
  blockquote{
    color: #868686;
    font-style: italic;
    padding-left: 15px;
    border-left: 2px solid #eaeaea;
    &:last-child{
      margin-bottom: 0;
    }
  }
  ul,ol{
    padding-left: 20px;
    text-align: left;
    li{
      margin-bottom: 5px;
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
  ol{
    counter-reset: item;
    list-style: none;
    padding-left: 0;
    li:before{
      content: counters(item, ".") ". ";
      counter-increment: item;
      font-weight: 600;
    }
    ol{
      padding-left: 15px;
    }
  }
  table{
    width: 100%;
    margin-bottom: 10px;
    td{
      vertical-align: top;
    }
  }
  p,ul,ol,table{
    &:last-child {
      margin-bottom: 0;
    }
  }
  img{
    height: auto;
  }
}
.text-add-position{
  font-size: 13px;
}
.text-add-member{
  font-size: 14px;
}
.description-project, .cmt {
  p{
    margin: 0;
  }
}
.required_note {
  font-style: italic;
  color: #686868;
}
.swal-custom {
  .swal2-content, .swal2-actions {
    font-size: 0.8rem!important;
    text-align: left!important;
    padding: 0!important;
  }
  .swal2-actions {
    justify-content: end;
    .swal2-confirm {
      background-color: #0791A3!important;
      min-width: 90px !important;
    }
    .swal2-cancel {
      background-color: white!important;
      color: #7C7C7C!important;
      border: solid 1px #DDDFE1;
      min-width: 90px !important;
      &:hover{
        background-color:#DDDFE1 !important;
      }
    }
  }
}
.height-52{
  height: 52px !important;
}
