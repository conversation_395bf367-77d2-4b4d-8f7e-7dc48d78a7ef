<?php

namespace App\Services;

use App\Models\UserDevice;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Exception\Messaging\AuthenticationError;
use Kreait\Firebase\Exception\Messaging\InvalidMessage;
use Kreait\Firebase\Exception\Messaging\NotFound;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class FCMService
{
    // Maximum tokens per batch
    private const MAX_TOKENS_PER_BATCH = 500;

    private $factory;
    private $fcmMessaging;

    public function __construct()
    {
        // Initialize Firebase Messaging
        $this->factory = (new Factory)->withServiceAccount(storage_path('app/firebase/firebase_credentials.json'));
        $this->fcmMessaging = $this->factory->createMessaging();
    }

    /**
     * Send notification to a list of device tokens
     */
    public function sendNotification(array $tokens, array $notification, array $data): array
    {
        // Remove duplicate and empty tokens
        $tokens = collect($tokens)->filter()->unique()->values()->toArray();
        if (empty($tokens)) {
            return [
                'total_sent'    => 0,
                'success_count' => 0,
                'failure_count' => 0,
                'failed_tokens' => [],
            ];
        }

        // Create Notification object
        $notification = Notification::fromArray($notification);
        $message = CloudMessage::new()
            ->withNotification($notification)
            ->withData($data)
            ->withDefaultSounds()
            ->withHighestPossiblePriority();
        // Add badge for ios if specified
        if (isset($data['badge'])) {
            $message = $message->withApnsConfig(
                ApnsConfig::new()->withBadge($data['badge'])
            );
        }

        $success = 0;
        $failure = 0;
        $failedTokens = [];

        // Split tokens into batches (max 500 tokens per request)
        foreach (array_chunk($tokens, self::MAX_TOKENS_PER_BATCH) as $tokensBatch) {
            $report = $this->fcmMessaging->sendMulticast($message, $tokensBatch);

            $success += $report->successes()->count();
            $failure += $report->failures()->count();

            // Handle failed tokens for removal from database
            foreach ($report->failures()->getItems() as $failureItem) {
                Log::debug('FCM Failure', [
                    'token' => $failureItem->target()->value(),
                    'error' => $failureItem->error(),
                ]);
                if (
                    $failureItem->error() instanceof InvalidMessage ||
                    $failureItem->error() instanceof NotFound ||
                    $failureItem->error() instanceof AuthenticationError
                ) {
                    $failedTokens[] = $failureItem->target()->value();
                }
            }
        }

        // Remove invalid tokens from database
        if (!empty($failedTokens)) {
            Log::debug('Removing failed FCM tokens from database', ['tokens' => $failedTokens]);
            UserDevice::whereIn('device_token', $failedTokens)->delete();
        }

        return [
            'total_sent'    => count($tokens),
            'success_count' => $success,
            'failure_count' => $failure,
            'failed_tokens' => $failedTokens,
        ];
    }
}
