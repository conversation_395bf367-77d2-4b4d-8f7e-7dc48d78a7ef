.timepicker{
    .input-group-addon{
        width: 40px;
        border-right: 1px solid #ced4da;
        border-top: 1px solid #ced4da;
        border-bottom: 1px solid #ced4da;
        background-color: #f3f0f0;
        position: relative;
        line-height: 33px;
    }
    .fa-clock{
        font-size:18px;
    }
    .glyphicon-calendar{
        position: absolute;
        left: 12px;
        top: 15px;
    }
    .btn[data-action=incrementHours] span::after {
        font-family: "Font Awesome 5 Pro";
        font-weight: 900;
        content: "\f106";
        font-size: 15px;
    }
    .btn[data-action=incrementMinutes] span::after{
        font-family: "Font Awesome 5 Pro";
        font-weight: 900;
        content: "\f106";
        font-size: 15px;
    }
    .btn[data-action=incrementSeconds] span::after{
        font-family: "Font Awesome 5 Pro";
        font-weight: 900;
        content: "\f106";
        font-size: 15px;
    }
    .btn[data-action=decrementHours] span::after{
        font-family: "Font Awesome 5 Pro";
        font-weight: 900;
        content: "\f107";
        font-size: 15px;
    }
    .btn[data-action=decrementMinutes] span::after{
        font-family: "Font Awesome 5 Pro";
        font-weight: 900;
        content: "\f107";
        font-size: 15px;
    }
    .btn[data-action=decrementSeconds] span::after{
        font-family: "Font Awesome 5 Pro";
        font-weight: 900;
        content: "\f107";
        font-size: 15px;
    }
}
.meeting {
    .input-meeting{
        margin-top: 2px;
    }
}