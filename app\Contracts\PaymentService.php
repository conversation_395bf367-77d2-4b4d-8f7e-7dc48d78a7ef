<?php

namespace App\Contracts;

use App\Enums\PaymentChannel;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Http\Request;

interface PaymentService
{
    /** Return the channel this service supports. */
    public function channel(): PaymentChannel;

    /** Create/initiate a payment for the given order and return redirect URL (if any). */
    public function initiate(Order $order, array $options = []): Payment;

    /** Build redirect URL to provider (for web redirect flows). */
    public function buildRedirectUrl(Payment $payment): string;

    /** Handle user return callback (synchronous) from provider. */
    public function handleReturn(Request $request): Payment;

    /** Handle IPN/webhook (asynchronous) from provider. */
    public function handleIpn(Request $request): Payment;

    /** Actively query provider to confirm payment status (optional). */
    public function query(Payment $payment): Payment;

    /** Expire payments that exceeded validity window. */
    public function expire(Payment $payment): Payment;
}
