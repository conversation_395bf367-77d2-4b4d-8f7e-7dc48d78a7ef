//
// Pages: Profile
//

.profile-user-img {
  border: 3px solid $gray-500;
  margin: 0 auto;
  padding: 3px;
  width: 100px;
  height: 100px;
  display: block;
  overflow: hidden;
  object-fit: cover;
}
.position-relative {
  .badge {
    background-color: rgba(255, 255, 255, 0) !important;
    color: black !important;
    font-weight: unset;
    font-size: 13px;
  }
}
.profile-username {
  font-size: 21px;
  margin-top: 5px;
  a {
    color: black;
  }
  
}

.post {
  border-bottom: 1px solid $gray-500;
  color: #666;
  margin-bottom: 15px;
  padding-bottom: 15px;

  &:last-of-type {
    border-bottom: 0;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .user-block {
    margin-bottom: 15px;
    width: 100%;
  }

  .row {
    width: 100%;
  }
}

.dark-mode {
  .post {
    color: $white;
    border-color: $gray-600;
  }
}
.img-user-information{
  width: 25px;
  height: 25px;
  margin-right: 5px;
}
.card-user-information{
  padding: 16px 11px;
  background-color: #0072bb;
}
.user-information-detail{
  display: flex;
  justify-content: center;
  align-items: center;
}