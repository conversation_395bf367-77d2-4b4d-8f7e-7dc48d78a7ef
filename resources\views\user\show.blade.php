@extends('layouts.master')
@section('title',trans('language.show_employee'))
@section('content')
    @php
        $request = request();
    @endphp
    <section class="content">
        {{ Breadcrumbs::render('user.show') }}
        <div class="clearfix"></div>
        <form method="POST" enctype="multipart/form-data">
            @if ($errors->all())
                <div class="note note-danger"><p>{{trans('message.fill_form_failed')}}</p></div>
            @elseif(session('status_update'))
                <div class="note note-success"><p>{{ session('status_update') }}</p></div>
            @else
                <div class="note note-success"><p>{{trans('message.show_employee_notify')}}</p></div>
            @endif
            <div class="row">
                <div class="col-md-9">
                    <div class="tabbable-custom ct-box-left-sticky">
                        <ul class="nav nav-tabs ">
                            <li class="nav-item">
                                <a href="#tab_detail" class="nav-link active show" data-toggle="tab">{{trans('language.detail')}}</a>
                            </li>
                        </ul>
                        @if(isset($user))
                        <div class="tab-content">
                            <div class="tab-pane active show" id="tab_detail">
                                <div class="form-group row d-flex justify-content-center">
                                    <div class="col-md-3">
                                        <div class="widget meta-boxes">
                                            <div class="">
                                                <div class="image-box">
                                                    <input id="avatar-image" type="file" name="avatar" value=""
                                                           class="avatar-image" style="display: none;" accept="image/*"
                                                           data-preview="avatar-holder">
                                                    <div class="preview-image-wrapper ">
                                                        <img id="avatar-holder" class="preview_image"
                                                             src="{{route('user.avatar', ['id' => $user->id])}}" type="text" alt="preview image">
                                                    </div>
                                                    <div class="image-box-actions">
                                                        <a id="lfm" data-input="avatar-image" class="btn btn-primary lfm fa-disabled">
                                                            <i class="fa fa-picture-o"></i> {{trans('language.avatar')}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="widget meta-boxes">
                                            <div class="">
                                                <div class="image-box">
                                                    <input id="face-image" type="file" name="face_image" value=""
                                                           class="image-face" style="display: none;" accept="image/*"
                                                           data-preview="face-holder" disabled>
                                                    <div class="preview-image-wrapper ">
                                                        <img id="face-holder" class="preview_image"
                                                             src="{{route('user.faceImage', ['id' => $user->id])}}" type="text" alt="preview image">
                                                    </div>
                                                    <div class="image-box-actions">
                                                        <a id="lfm" data-input="face-image" class="btn btn-success lfm fa-disabled">
                                                            <i class="fa fa-user-circle"></i> {{trans('language.face_image')}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="form-group col-md-4">
                                        <label for="first_name" class="control-label">{{trans('language.first_name')}}</label>
                                        <input type="text" class="form-control" name="first_name" id="first_name" autocomplete="off" disabled
                                               @if(old('first_name')) value="{{old('first_name')}}" @else value="{{ $user->first_name }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="last_name" class="control-label">{{trans('language.last_name')}}</label>
                                        <input type="text" class="form-control" name="last_name" id="last_name" autocomplete="off" disabled
                                               @if(old('last_name')) value="{{old('last_name')}}" @else value="{{ $user->last_name }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="check_name" class="control-label">{{trans('language.check_name')}}</label>
                                        <input type="text" class="form-control" name="check_name" id="check_name" autocomplete="off" disabled
                                               @if(old('check_name')) value="{{old('check_name')}}" @else value="{{ $user->name }}" @endif>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="form-group col-md-4">
                                        <label for="email" class="control-label">{{trans('language.email')}}</label>
                                        <input type="email" class="form-control" name="email" id="email" autocomplete="off" disabled
                                               @if(old('email')) value="{{old('email')}}" @else value="{{ $user->email }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="personal_email" class="control-label">{{trans('language.personal_email')}}</label>
                                        <input type="email" class="form-control" name="personal_email" id="personal_email" autocomplete="off" disabled
                                               @if(old('personal_email')) value="{{old('personal_email')}}" @else value="{{ $user->personal_email }}" @endif>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="form-group col-md-4">
                                        <label for="gender" class="control-label">{{trans('language.gender')}}</label>
                                        <input type="text" class="form-control" name="gender" id="gender" disabled
                                               @if(isset(trans('language.genders')[$user->gender])) value="{{trans('language.genders')[$user->gender]}}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="birthday" class="control-label">{{trans('language.birthday')}}</label>
                                        <input type="text" class="form-control" name="birthday" id="birthday" autocomplete="off" disabled
                                               @if(old('birthday')) value="{{old('birthday')}}" @else value="{{ $user->birthday }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="phone" class="control-label">{{trans('language.phone')}}</label>
                                        <input type="text" class="form-control" name="phone" id="phone" autocomplete="off" disabled
                                               @if(old('phone')) value="{{old('phone')}}" @else value="{{ $user->phone }}" @endif>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="form-group col-md-4">
                                        <label for="department" class="control-label">{{trans('language.department')}}</label>
                                        @php
                                            $department = \App\Models\Department::find($user->department_id);
                                        @endphp
                                        <input type="text" class="form-control" name="department" id="department" disabled
                                               @if(isset($department)) value="{{$department->name}}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="position" class="control-label">{{trans('language.position')}}</label>
                                        @php
                                            $position = \App\Models\Position::find($user->position_id);
                                        @endphp
                                        <input type="text" class="form-control" name="position" id="position" disabled
                                               @if(isset($position)) value="{{$position->name}}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="working_type" class="control-label">{{trans('language.working_type')}}</label>
                                        <input type="text" class="form-control" name="working_type" id="working_type" disabled
                                               @if(isset(trans('language.working_types')[$user->working_type])) value="{{trans('language.working_types')[$user->working_type]}}" @endif>
                                    </div>
                                </div>
                                <div>
                                    <label for="hometown" class="control-label">{{trans('language.hometown')}}</label>
                                    <div class="form-group row">
                                        @php
                                            $prefecture = \App\Models\Prefecture::find($user->prefecture_id);
                                            $district = \App\Models\District::find($user->district_id);
                                            $commune = \App\Models\Commune::find($user->commune_id);
                                        @endphp
                                        <div class="form-group col-md-4">
                                            <input type="text" class="form-control" name="prefecture_id" id="prefecture_id" disabled
                                                   @if(isset($prefecture)) value="{{$prefecture->name}}" @endif>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <input type="text" class="form-control" name="district_id" id="district_id" disabled
                                                   @if(isset($district)) value="{{$district->name}}" @endif>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <input type="text" class="form-control" name="commune_id" id="commune_id" disabled
                                                   @if(isset($commune)) value="{{$commune->name}}" @endif>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="address" class="control-label">{{trans('language.address')}}</label>
                                    <input type="text" class="form-control" name="address" id="address" autocomplete="off" disabled
                                           @if(old('address')) value="{{old('address')}}" @else value="{{ $user->address }}" @endif>
                                </div>
                                <div class="form-group row">
                                    <div class="form-group col-md-4">
                                        <label for="identity_card" class="control-label">{{trans('language.identity_card')}}</label>
                                        <input type="text" class="form-control" name="identity_card" id="identity_card" autocomplete="off" disabled
                                               @if(old('identity_card')) value="{{old('identity_card')}}" @else value="{{ $user->identity_card }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="id_issued_place" class="control-label">{{trans('language.id_issued_place')}}</label>
                                        <input type="text" class="form-control" name="id_issued_place" id="id_issued_place" autocomplete="off" disabled
                                               @if(old('id_issued_place')) value="{{old('id_issued_place')}}" @else value="{{ $user->id_issued_place }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="id_issued_at" class="control-label">{{trans('language.id_issued_at')}}</label>
                                        <input type="text" class="form-control" name="id_issued_at" id="id_issued_at" autocomplete="off" disabled
                                               @if(old('id_issued_at')) value="{{old('id_issued_at')}}" @else value="{{ $user->id_issued_at }}" @endif>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="banking_account" class="control-label">{{trans('language.banking_account')}}</label>
                                    <textarea class="form-control" name="banking_account" id="banking_account" disabled>@if(old('banking_account')){!! old('banking_account') !!}@else{!! App\Helpers\StringHelper::escapeHtml($user->banking_account) !!}@endif</textarea>
                                </div>
                                <div class="form-group row">
                                    <div class="form-group col-md-4">
                                        <label for="started_at" class="control-label">{{trans('language.started_at')}}</label>
                                        <input type="text" class="form-control" name="started_at" id="started_at" autocomplete="off" disabled
                                               @if(old('started_at')) value="{{old('started_at')}}" @else value="{{ $user->started_at }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="signed_at" class="control-label">{{trans('language.signed_at')}}</label>
                                        <input type="text" class="form-control" name="signed_at" id="signed_at" autocomplete="off" disabled
                                               @if(old('signed_at')) value="{{old('signed_at')}}" @else value="{{ $user->signed_at }}" @endif>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="ended_at" class="control-label">{{trans('language.ended_at')}}</label>
                                        <input type="text" class="form-control" name="ended_at" id="ended_at" autocomplete="off" disabled
                                               @if(old('ended_at')) value="{{old('ended_at')}}" @else value="{{ $user->ended_at }}" @endif>
                                    </div>
                                </div>
                            </div>
                        </div><!-- end.tab-content -->
                        @endif
                    </div>
                </div>
                <div class="col-md-3 right-sidebar sticky-top">
                    <div class="ct-sticky">
                        <div class="widget meta-boxes form-actions form-actions-default action-horizontal">
                            <div class="widget-title">
                                <h4><span>{{trans('language.operation')}}</span></h4>
                            </div>
                            <div class="widget-body">
                                <div class="btn-set">
                                    <a class="btn btn-primary"
                                       href="{{route('back', ['key' => 'redirect.admin.user.show', 'default_redirect_url' => route('admin.user.index')])}}">
                                        <i class="fa fa-reply"></i> {{trans('language.return')}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </section>
@stop
@section('addjs')
    <script src="{{asset('js/ScrollMagic.min.js')}}"></script>
    <script type="text/javascript">
        // Scroll left operation block
        $(document).ready(function () {
            const postDetails = document.querySelector(".ct-box-left-sticky");
            const postSidebar = document.querySelector(".ct-sticky");
            const postSidebarContent = document.querySelector(".ct-sticky > div");
            const controller = new ScrollMagic.Controller();
            const scene = new ScrollMagic.Scene({
                triggerElement: postSidebar,
                triggerHook: 0,
                offset: -20,
                duration: getDuration
            }).addTo(controller);
            if (window.matchMedia("(min-width: 768px)").matches) {
                scene.setPin(postSidebar, {pushFollowers: false});
            }
            // In your projects, you might want to debounce resize event for better performance
            window.addEventListener("resize", () => {
                if (window.matchMedia("(min-width: 768px)").matches) {
                    scene.setPin(postSidebar, {pushFollowers: false});
                } else {
                    scene.removePin(postSidebar, true);
                }
            });
            function getDuration() {
                return postDetails.offsetHeight - postSidebarContent.offsetHeight;
            }
        });
    </script>
@stop