@php
    $paymentStatus = $payment->meta['provider_return_status'] ?? null;
    $isPaymentSuccess = $paymentStatus === \App\Enums\PaymentStatus::SUCCESS->value;
@endphp
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('language.payment_result') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .icon-animate {
            animation: pop 0.5s cubic-bezier(.36,1.64,.56,1) both;
        }
        @keyframes pop {
            0% { transform: scale(0.5); opacity: 0; }
            80% { transform: scale(1.15); opacity: 1; }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-0 sm:p-0">
    <div class="bg-white shadow-lg rounded-none sm:rounded-lg p-4 sm:p-8 w-full min-h-screen sm:min-h-0 sm:max-w-md text-center flex flex-col justify-center">
        <div class="flex justify-center mb-6">
            @if($isPaymentSuccess)
                <span class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 icon-animate">
                    <svg class="w-12 h-12 text-green-500" fill="none" stroke="currentColor" stroke-width="3" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M7 13l3 3 7-7"/>
                    </svg>
                </span>
            @else
                <span class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-100 icon-animate">
                    <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" stroke-width="3" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </span>
            @endif
        </div>
        <h1 class="text-2xl font-bold mb-6 text-gray-800">{{ __('language.payment_result') }}</h1>
        <div class="mb-4">
            <span class="text-gray-600">{{ __('language.status') }}:</span>
            @if($isPaymentSuccess)
                <span class="ml-2 text-green-600 font-semibold text-lg flex items-center justify-center gap-2">
                    {{ __('message.payment_success') }}
                </span>
            @else
                <span class="ml-2 text-red-600 font-semibold text-lg flex items-center justify-center gap-2">
                    {{ __('message.payment_failure') }}
                </span>
            @endif
        </div>
    </div>
    <script>
        const result = {
            status: "{{ $paymentStatus }}",
            message: "{{ $isPaymentSuccess ? __('message.payment_success') : __('message.payment_failure') }}",
        }
        if (window.Unity && typeof window.Unity.call === "function") {
            window.Unity.call(JSON.stringify(result));
        }
    </script>


</html></body></body>
</html>
