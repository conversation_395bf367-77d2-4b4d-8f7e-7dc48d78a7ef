.avatar-list {
  margin: 0 .5rem 0 0;
  padding: 0;
  font-size: 0;

  .avatar{
    text-align: center;
    color: #fff;
    width: 2rem;
    height: 2rem;
    line-height: 3rem;
    border-radius: 50%;
    display: inline-block;
    background: #808488 no-repeat center/cover;
    position: relative;
    vertical-align: bottom;
    font-size: .875rem;
    user-select: none;
    object-fit: cover;
    &:not(:last-child) {
      margin-right: .5rem;
    }
    &-sm {
      width: 1.5rem;
      height: 1.5rem;
      line-height: 1.5rem;
      font-size: .75rem;
    }
  }

  &.avatar-list-stacked{
    .avatar {
      box-shadow: 0 0 0 2px #fff;
      margin-right: -.5rem !important;
    }
  }
}

.img-avatar {
    object-fit: cover;
}
