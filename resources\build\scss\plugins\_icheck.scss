//
// Plugin: iCheck Bootstrap
//
[class*=icheck-]>.label-icheck{
  padding-left: 29px!important;
  min-height: 22px;
  line-height: 22px;
  display: inline-block;
  position: relative;
  vertical-align: top;
  margin-bottom: 0;
  font-weight: inherit;
  cursor: pointer;

  &:before, &:after{
    content: '';
  }
}


[class*=icheck-]>input:first-child+input[type=hidden]+.label-icheck::before,
[class*=icheck-]>input:first-child+.label-icheck::before{
  content: "";
  display: inline-block;
  position: absolute;
  width: 22px;
  height: 22px;
  border: 1px solid #D3CFC8;
  margin-left: -29px;
  border-radius: 3px;
}

[class*=icheck-]>input:first-child:checked+input[type=hidden]+.label-icheck::after,
[class*=icheck-]>input:first-child:checked+.label-icheck::after{
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  width: 7px;
  height: 10px;
  border: 2px solid #fff;
  border-left: none;
  border-top: none;
  transform: translate(7.75px,4.5px) rotate(45deg);
}

// iCheck colors (theme colors)
@each $name, $color in $theme-colors {
  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + .label-icheck::before,
  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + .label-icheck::before {
    border-color: #{$color};
  }

  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + .label-icheck::before,
  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + .label-icheck::before {
    border-color: #{$color};
  }

  .icheck-#{$name} > input:first-child:checked + .label-icheck::before,
  .icheck-#{$name} > input:first-child:checked + input[type="hidden"] + .label-icheck::before {
    background-color: #{$color};
    border-color: #{$color};
  }
}

// iCheck colors (colors)
@each $name, $color in $colors {
  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + .label-icheck::before,
  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + .label-icheck::before {
    border-color: #{$color};
  }

  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + .label-icheck::before,
  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + .label-icheck::before {
    border-color: #{$color};
  }

  .icheck-#{$name} > input:first-child:checked + .label-icheck::before,
  .icheck-#{$name} > input:first-child:checked + input[type="hidden"] + .label-icheck::before {
    background-color: #{$color};
    border-color: #{$color};
  }
}

.dark-mode {
  [class*="icheck-"] > input:first-child:not(:checked) {
    + input[type="hidden"] + .label-icheck::before,
    + .label-icheck::before {
      border-color: $gray-600;
    }
  }
}

[class*=icheck-]>input:first-child:disabled{
  cursor: no-drop;
}


