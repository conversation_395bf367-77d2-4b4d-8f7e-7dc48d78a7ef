<?php

namespace App\Jobs;

use App\Services\SmsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SendSmsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const RATE_LIMIT_CACHE_KEY = 'sms_rate_limit';
    const MAX_SMS_PER_SECOND = 25;

    protected string $templateId;
    protected array $params;
    protected array $phones;
    protected string $reqIdPrefix;
    protected string $code;
    protected array $options;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $templateId,
        array $params,
        array $phones,
        string $reqIdPrefix = 'SMS',
        string $code = '',
        array $options = []
    ) {
        $this->templateId = $templateId;
        $this->params = $params;
        $this->phones = $phones;
        $this->reqIdPrefix = $reqIdPrefix;
        $this->code = $code;
        $this->options = $options;

        // Set queue priority - OTP gets highest priority
        if ($reqIdPrefix === 'OTP') {
            $this->onQueue('sms-priority');
        } else {
            $this->onQueue('sms-normal');
        }
    }

    /**
     * Execute the job.
     */
    public function handle(SmsService $smsService): void
    {
        $phoneCount = count($this->phones);
        
        Log::info('SMS Job Started', [
            'phone_count' => $phoneCount,
            'template_id' => $this->templateId,
            'req_prefix' => $this->reqIdPrefix
        ]);

        // Check rate limit
        if (!$this->checkRateLimit($phoneCount)) {
            Log::warning('SMS Rate limit exceeded, delaying job', [
                'phone_count' => $phoneCount,
                'current_count' => $this->getCurrentSmsCount()
            ]);
            
            // Delay job by 1 second and retry
            $this->release(1);
            return;
        }

        try {
            // Build payload (copied from SmsService->send() private method)
            $payload = [
                "RQST" => [
                    "name" => "send_sms_list",
                    "REQID" => (string) $smsService->generateRequestId($this->reqIdPrefix, $this->code),
                    "LABELID" => (string) $smsService->getLabelId(),
                    "CONTRACTTYPEID" => (string)($this->options['contractType'] ?? "1"),
                    "CONTRACTID" => (string) $smsService->getContractId(),
                    "TEMPLATEID" => (string) $this->templateId,
                    "PARAMS" => $this->params,
                    'SCHEDULETIME' => $this->options['scheduleTime'] ?? '',
                    'MOBILELIST' => $smsService->convertPhonesToString($this->phones),
                    'ISTELCOSUB' => "0",
                    'AGENTID' => (string) $smsService->getAgentId(),
                    'APIUSER' => $smsService->getApiUser(),
                    'APIPASS' => $smsService->getApiPass(),
                    'USERNAME' => $smsService->getUsername(),
                    'DATACODING' => (string)($this->options['dataCoding'] ?? "0"),
                ]
            ];

            // Add optional fields if provided
            if (!empty($this->options['saleOrderId'])) {
                $payload['RQST']['SALEORDERID'] = $this->options['saleOrderId'];
            }

            if (!empty($this->options['packageId'])) {
                $payload['RQST']['PACKAGEID'] = $this->options['packageId'];
            }

            // Log request
            Log::info('SMS Request', [
                'request_id' => $payload['RQST']['REQID'],
                'phone_count' => $phoneCount,
                'template_id' => $this->templateId
            ]);

            Log::debug('SMS API Payload', [
                'json' => json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT),
                'payload' => $payload
            ]);

            // Make API call
            $response = $smsService->makeApiCall($payload);
            $errorCode = (int)$response['RPLY']['ERROR'];

            // Log error only when code != 0
            if ($errorCode !== 0) {
                Log::error('SMS API Error', [
                    'request_id' => $payload['RQST']['REQID'],
                    'error_code' => $errorCode,
                    'error_desc' => $response['RPLY']['ERROR_DESC'],
                    'response' => $response
                ]);
            }

            // Update rate limit counter
            $this->updateRateLimit($phoneCount);

            Log::info('SMS Job Completed', [
                'phone_count' => $phoneCount,
                'error_code' => $errorCode,
                'response' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('SMS Job Failed', [
                'phone_count' => $phoneCount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // Re-throw to trigger job retry
        }
    }

    /**
     * Check if we can send SMS without exceeding rate limit
     */
    private function checkRateLimit(int $phoneCount): bool
    {
        $currentCount = $this->getCurrentSmsCount();
        return ($currentCount + $phoneCount) <= self::MAX_SMS_PER_SECOND;
    }

    /**
     * Get current SMS count in this second
     */
    private function getCurrentSmsCount(): int
    {
        $cacheKey = self::RATE_LIMIT_CACHE_KEY . ':' . now()->format('Y-m-d-H-i-s');
        return Cache::get($cacheKey, 0);
    }

    /**
     * Update rate limit counter
     */
    private function updateRateLimit(int $phoneCount): void
    {
        $cacheKey = self::RATE_LIMIT_CACHE_KEY . ':' . now()->format('Y-m-d-H-i-s');
        $currentCount = Cache::get($cacheKey, 0);
        Cache::put($cacheKey, $currentCount + $phoneCount, 2); // Cache for 2 seconds
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SMS Job Failed Permanently', [
            'phone_count' => count($this->phones),
            'template_id' => $this->templateId,
            'error' => $exception->getMessage(),
            'phones' => $this->phones
        ]);
    }
}
