function ScrollSidebar() {
    if ($(".layout-fixed .sidebar").length) {
        $(".layout-fixed .sidebar").overlayScrollbars({
            className: "os-theme-light",
            sizeAutoCapable: true,
            scrollbars: {
                autoHide: "l",
                clickScrolling: true
            }
        });
    }
}

function SidebarOverlay() {
    $("#sidebar-overlay").on("click", function() {
        if ($(".sidebar-mini").length) {
            $(".sidebar-mini").toggleClass(
                "sidebar-open sidebar-closed sidebar-collapse"
            );
            replayTheiaStickySidebar();
        }
    });
}

function theiaStickySidebar() {
    if ($(".theia-content").length && $(".theia-sidebar").length) {
        $(".theia-content, .theia-sidebar").theiaStickySidebar({
            additionalMarginTop: $("nav.main-header").height() + 30,
            additionalMarginBottom: 5
        });
    }
}

function destroyTheiaStickySidebar() {
    if ($(".theia-content").length && $(".theia-sidebar").length) {
        $(".theia-sidebar, .theia-content").removeAttr("style");
    }
}

function replayTheiaStickySidebar() {
    new Promise((resolve, reject) => {
        destroyTheiaStickySidebar();
        resolve();
    }).then(() => {
        setTimeout(theiaStickySidebar, 300);
    });
}

export function scrollToTop(e) {
    e.preventDefault();
    let $href = $($(this).attr("href"));
    if ($href.is(":hidden")) {
        $href.show();
    }
    if ($href.length) {
        $("html, body").animate({
                scrollTop: $href.offset().top - $("nav.main-header").height() - 30
            },
            500
        );
    }
}

function hoverTable() {
    $(".table-responsive.table-projects :not(.not_box_shadow) thead tr").each(
        function() {
            $(this)
                .find("th")
                .first()
                .attr("colspan", 2);
        }
    );
    $(".table-responsive.table-projects :not(.not_box_shadow) tbody tr").each(
        function() {
            if (!$(this).find(".box-shadow").length) {
              $(this)
                .find("td")
                .first()
                .after("<td class='box-shadow'></td>");
            }
        }
    );
    $(".table-responsive.table-projects :not(.not_box_shadow) tbody tr").each(
        function() {
            $(this).css("--height", $(this).height() + "px");
        }
    );

    $(".table-projects.table-responsive.table-list-task tfoot th").first().attr("colspan", 7);
    window.addEventListener(
        "resize",
        function() {
            $(
                ".table-responsive.table-projects :not(.not_box_shadow) tbody tr"
            ).each(function() {
                $(this).css("--height", $(this).height() + "px");
            });
        },
        true
    );
}

function SidebarCollapse(){
    var widthScreen = $(window).width();
    var deviceComputer = 991;
    if(widthScreen > deviceComputer){
        var isSidebarCollapse = $('body').hasClass('sidebar-collapse');
        if(isSidebarCollapse){
            $('.logo-sidebar-mini').addClass("d-none");
            $('.logo-sidebar-large').removeClass("d-none");
        }else{
            $('.logo-sidebar-mini').removeClass("d-none");
            $('.logo-sidebar-large').addClass("d-none");
        }
    }
}

function Layout() {
    ScrollSidebar();
    theiaStickySidebar();
    SidebarOverlay();
    hoverTable();

    // events
    $(document).on("click", '[data-scroll="top"]', scrollToTop);
    $('[data-widget="pushmenu"]').on("click", replayTheiaStickySidebar);
    $(window).on("resize", replayTheiaStickySidebar);
    $(document).on("shown.bs.tab", '.nav-link', hoverTable);
    $('.modal').on('show.bs.modal', function (e) {
        $(".table-list-data tr").addClass("modal-open");
    })
    $('.modal').on('hide.bs.modal', function (e) {
        $(".table-list-data tr").removeClass("modal-open");
    })
    $('.aside-pushmenu').on('click',function(){
        SidebarCollapse()
        setTimeout(()=> {
            $('#double-scroll').trigger('resize.doubleScroll');
        },500);
    })
    $('[data-widget="pushmenu"]').on('click',function(){
        SidebarCollapse()
    })
}

export default Layout;