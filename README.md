# BEEFARM

---

## 🚀 Requirements

- **PHP**: 8.2  
- **Composer**: >= 2.x  
- **<PERSON><PERSON>**: 12.x  
- **Database**: MySQL 8.4  
- **Node.js**: >= 22.x (with npm)  
- **Additional Services**: Queue worker

---

## ⚙️ Installation

### 1. Clone the repository
```bash
git clone https://github.com/bangsep/beefarm_be.git
cd beefarm_be
```

### 2. Install dependencies
```bash
composer install
npm install
npm run plugins
```

### 3. Configure environment
Copy `.env.example` to `.env`:
```bash
cp .env.example .env
```
Update environment variables in `.env` file:

### 4. Generate application key
```bash
php artisan key:generate
```

### 5. Run migrations & seeders
Import 2 files database\migrations\provinces.sql, wards.sql to DB
```bash
php artisan migrate --seed
```

### 6. Start the development server
```bash
php artisan serve
npm run dev
```

### 7. Access to the website
- Open link: http://127.0.0.1:8000/user
- Login with the following account: </br>
    **Email:** <EMAIL> </br>
    **Password:** password