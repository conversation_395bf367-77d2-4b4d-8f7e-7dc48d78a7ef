<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\OrderType;
use Illuminate\Validation\Rules\Enum;

class OrderRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type'     => ['nullable', new Enum(OrderType::class)],
            'amount'   => ['required', 'integer', 'min:0'],
            'currency' => ['nullable', 'string', 'size:3'],
            'meta'     => ['nullable', 'array'],
        ];
    }
}
