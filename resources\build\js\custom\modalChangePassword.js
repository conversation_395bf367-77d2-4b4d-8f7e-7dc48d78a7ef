export default function ModalChangePassword(){
    $(document).on('submit', '.form-change-password', submitChangePassword);
    $(document).on('hide.bs.modal', '#modalChangePassword', hideModalChangePassword);
}

function submitChangePassword(e){
    e.preventDefault();
    let form = $(this),
        msg = form.find('.alert-message'),
        token = $('meta[name="csrf-token"]').length ? $('meta[name="csrf-token"]').attr('content') : '';

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': token
        },
        url: form.attr('action'),
        type: form.attr('method'),
        dataType: "JSON",
        data: new FormData($(this)[0]),
        contentType: false,
        cache: false,
        processData: false,
        success: function (data){
            if (data.status === 200){
                msg.addClass('alert-success show').removeClass('d-none alert-danger').text(data.msg);
                setTimeout(function (){
                    $('#modalChangePassword').modal('hide');
                },2000)
            } else {
                msg.addClass('alert-danger show').removeClass('d-none alert-success').text(data.msg);
                $('.form-change-password').find('.btn-primary').prop('disabled', false);
            }
        },
        error: function (err){
            msg.addClass('alert-danger show').removeClass('d-none alert-danger').text(err);
        }
    })
}

function hideModalChangePassword(){
    let modal = $(this);
    modal.find('input').val('');
    modal.find('.alert-message').removeClass('alert-success alert-danger show').addClass('d-none').text('');
    modal.find('.btn-primary').prop('disabled', false);
}