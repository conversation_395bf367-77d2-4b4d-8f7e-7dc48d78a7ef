
let $buttonRemove = $('<span class="text-red button-remove-value-select2"><i class="far fa-times"></i></span>');

export default function addButtonRemoveValueSelect2(){
    $(document).on('mousemove', 'span.select2.select2-container', appendButtonRemove)
    $(document).on('mouseleave', 'span.select2.select2-container', removeButtonRemove)
    $(document).on('click', 'span.select2.select2-container .button-remove-value-select2', removeValueSelect2)
}

function appendButtonRemove(){
    let select2 = $(this),
        select = select2.prev(),
        required = select.attr('required'),
        multiple = select.attr('multiple');
    if(!select2.hasClass("select2-container--disabled")){
        if ((typeof required === 'undefined' || required === false || (required && multiple)) && select.val()){
            select2.prepend($buttonRemove);
        }
    
    }
}

function removeButtonRemove(){
    $(this).find('.button-remove-value-select2').remove()
}

function removeValueSelect2(e){
    $(this).parent().prev().val([]).trigger('change');
    $(this).remove();
}