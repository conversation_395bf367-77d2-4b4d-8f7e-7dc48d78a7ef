@php
    $sortURL = route('versions.index');
@endphp
@if ($isFilter)
    <div class="mb-2">
        {{ trans('language.filter_mode') }}: {!! $isFilter !!}
    </div>
@endif
@php
    $platforms = [
        'ios' => ['icon' => 'fab fa-apple fa-lg mr-1 text-secondary'],
        'android' => ['icon' => 'fab fa-android fa-lg mr-1 text-success']
    ];
@endphp
<div class="d-flex align-items-center mb-2">
    <span class="mr-1">{{ __('language.latest_versions') }}:</span>

    @foreach ($platforms as $key => $data)
        @if (isset($latestVersions[$key]))
            <a href="{{ route('versions.edit', $latestVersions[$key]->id) }}" class="badge badge-light border mr-2">
                <i class="{{ $data['icon'] }}"></i> {{ $latestVersions[$key]->version }}
            </a>
        @else
            <span class="badge badge-light border mr-2">
                <i class="{{ $data['icon'] }}"></i> {{ __('language.not_available') }}
            </span>
        @endif
    @endforeach
</div>
<div class="table-list-data">
    @if (isset($versions) && count($versions) > 0)
        <div class="table-responsive table-projects" id="double-scroll">
            <table class="table table-hover table-valign-middle min-width-1200">
                <thead class="text-center text-nowrap">
                    <tr class="text-nowrap">
                        <th class="stt-order cursor_pointer" data-toggle="tooltip" data-original-title="STT">
                            {{ __('language.number_orders') }}
                        </th>
                        <th data-toggle="tooltip" data-original-title="{{ __('language.version_name') }}">
                            {{ __('language.version_name') }}
                        </th>
                        <th data-toggle="tooltip" data-original-title="{{ __('language.version_platform') }}">
                            {{ __('language.version_platform') }}
                        </th>
                        <th data-toggle="tooltip" data-original-title="{{ __('language.status') }}">
                            {{ __('language.status') }}
                        </th>
                        <th data-toggle="tooltip"
                            data-original-title="{{ __('language.version_header_description') }}">
                            {{ __('language.version_header_description') }}
                        </th>
                        <th data-toggle="tooltip"
                            data-original-title="{{ __('language.version_header_release_notes') }}">
                            {{ __('language.version_header_release_notes') }}
                        </th>
                        <th data-toggle="tooltip" data-original-title="Thao tác">
                            {{ trans('language.action') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($versions as $key => $version)
                        <tr class="list_data_asset">
                            <td class="text-center">
                                <span>{{ ($versions->currentPage() - 1) * $versions->perPage() + $key + 1 }}</span>
                            </td>
                            <td class="text-center">
                                {{ $version['version'] }}
                            </td>
                            <td class="text-center">
                                {{ $version['platform_name'] }}
                            </td>
                            <td class="text-center">
                                {{ $version['status_name'] }}
                            </td>
                            <td>
                                <div class="text-limit-line-10">{{ $version['description'] }}</div>
                            </td>
                            <td>
                                <div class="text-limit-line-10">{{$version['release_notes']}}</div>
                            </td>
                            <td class="text-center">
                                <a href="{{ route('versions.edit', $version['id']) }}" data-toggle="tooltip"
                                    data-original-title="{{ trans('language.edit') }}"
                                    class="edit text-md text-primary mr-2">
                                    <i class="far fa-pen-alt"></i>
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="pb-4">
            {{ $versions->appends(request()->query())->links('partials.pagination') }}
        </div>
    @else
        @include('partials.no-data-found')
    @endif
</div>
