.navbar-expand .navbar-nav .dropdown-menu {
    border: 0;
    min-width: 360px !important;
        max-width: 360px !important;
    .dropdown-header {
        
        background: #0791A3 0% 0% no-repeat padding-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 18px 25px;

        span {
            font-size: 20px;
            color: #FFFFFF;
        }
    }
}

.header-color{
    font-size: 14px;
    color: black;
}
.navbar-expand .navbar-nav .nav-link{
    padding-left: 0rem;
    padding-right: 0rem;
    
    &.title{
        top:1px;
        font-size: 16px;
        //font-weight: bold;
        margin: 0;
        text-align: left;
        color: #464A53;
        opacity: 1;
        
        &::before{
            //content: "/";
            //margin-right: 1rem;
        }
    }
}
.main-header .nav-link {
    height: auto;
}
.main-header.navbar .navbar-nav a.nav-link {
    max-width: inherit;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.size-46 {
    width: 40px;
    height: 40px;
}
.size-46 {
    width: 46px;
    height: 46px;
}
.size-80 {
    width: 80px;
    height: 80px;
}
.border-radius-12 {
    border-radius: 12px;
    overflow: hidden;
}

.dropdown-menu-content {
    line-height: normal;
    text-align: center;
    background-color: white;
    
    .dropdown-item-title {
        text-align: center;
        font-size: 14px;
        color: #0791A3;
        font-weight: normal;
        margin-top: 14px;
        margin-bottom: 8px;
    }
    
    p {
        font-size: 13px;
        margin-bottom: 5px;
        color: #4D4D4D;
        
        &.blue {
            color: #0791A3;
        }
    }
    .form-image__label{
        width: 24px;
        height: 25px;
        border: 1px solid #7C7C7C;
        line-height: 22px;
        bottom: 8px;
        right: 8px;
        color: #7C7C7C;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}

.dropdown-user-list {
    ul {
        margin: 0;
        padding: 0;
    }
    
    li {
        list-style: none;
        display: block;
        
        &:first-child {
            border-top: 1px solid #ccc;
        }
        
        &:not(:last-child) {
            border-bottom: 1px solid #ccc;
        }
        
        
        &:hover {
            background: rgba(124,124,124,0.1) 0% 0% no-repeat padding-box;
            
            a.language {
                span.label {
                    background: #fff;
                }
            }
        }
        
        img {
            margin-right: 15px;
            min-width: 23px;
        }
    }
    
    li a.language {
        justify-content: space-between;
        display: flex;
        width: 100%;
        
        span.label {
            background: #F2F2F2 0% 0% no-repeat padding-box;
            border-radius: 60px;
            color: #6A707E;
            font-size: 12px;
            font-weight: normal;
            line-height: inherit;
            padding: 5px 10px;
        }
    }
    a {
        display: flex;
        align-items: center;
        padding: 12px 25px;
        line-height: initial;
        color: #4D4D4D;
        flex-grow: 1;
    }
}

.main-header {
    .navbar-nav {
        align-items: center;
        gap: 1rem;
        
        .fa-bell,
        .fa-bookmark,
        .fa-thumbtack {
            font-size: 23px;
            color: #DDDFE1;
        }
        
        .badge {
            background: #FF0000 0% 0% no-repeat padding-box;
            border: 2px solid #fff;
            opacity: 1;
            border-radius: 50%;
            text-indent: -9999px;
            padding: 0;
            display: block;
            width: 12px;
            height: 12px;
            top: 4px;
        }
        
        .nav-item.show,
        .nav-item:hover {
            .fa-bell,
            .fa-bookmark,
            .fa-thumbtack {
                color: #7c7c7c;
            }
        }
    }
}

.dropdown-menu-lg {
    min-width: 420px;
    max-width: 420px;
}

.dropdown-menu {
    .card-notice__item {
        padding: 18px;
    }

    .card-notice__item .notice-info__wrap .notice-info__right .btn {
        height: 24px;
        font-size: 14px;
        min-height: 24px;
        padding: 0;
        margin-bottom: 0;
    }

    .card-notice__item .card-notice__avatar {
        margin-right: 20px;
    }
}

ul.project-pin__list {
    margin: 0;
    padding: 0;
    
    li {
        color: #6A707E;
        font-size: 18px;
        padding: 25px 28px;
        
        a {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .project-pin__name img {
            margin-right: 10px;
        }
        
        &:hover {
            background: #0000001A;
        }
    }
    
    li:not(:last-child) {
        border-bottom: 1px solid #ccc;
    }
}

.main-header.navbar .navbar-nav .project-pin__list a {
    color: #6A707E;
}

ul.bookmark-task__list {
    margin: 0;
    padding: 0;

    li {
        color: #6A707E;
        font-size: 18px;
        padding: 25px 28px;

        a {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #6A707E;
        }

        &:hover {
            background: #0000001A;
        }
        
        .task-name {
            font-size: 16px;
        }
        
        .task-datetime {
            font-size: 14px;
        }
        
        .badge {
            padding: 5px 10px !important;
            border-radius: 40px !important;
            display: initial !important;
            border: none!important;
            margin-left: 7px;
            min-width: 100px;
        }
    }

    li:not(:last-child) {
        border-bottom: 1px solid #ccc;
    }
}