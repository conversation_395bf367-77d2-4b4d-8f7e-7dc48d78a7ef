@extends('layouts.master')
@section('title',trans('language.user_management'))
@section('meta')
@stop

@section('css_library')
    @include('partials.style-library', ['datepicker' => true, 'fancybox' => true, 'select2' => true, 'icheck' => true])
@stop

@section('css_page')
@stop

@section('header')
    <li class="nav-item">
        {{ trans('language.user_management') }}
    </li>
@endsection
@section('content')
    @php
        $request = request();
    @endphp
    <section class="content pt-3">
        @include('partials.breadcrumb', [
            'item' => '<a href="' . route('user.index') . '">' . trans('language.user_management') . '</a>
            &nbsp;/&nbsp;' . trans('language.user_list')
        ])
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-end flex-wrap">
                <div class="mr-1 mb-2">
                    <a href="{{route('user.index',['refresh'=>'true'])}}" class="btn btn-default text-white d-none d-sm-inline-block"><i class="fal fa-sync"></i> {{trans('language.refresh')}}</a>
                    <a href="#collapseFilter" class="btn btn-default text-white" data-toggle="collapse"><i class="far fa-filter"></i> {{trans('language.filter')}}</a>
                </div>
                <div class="actions mb-2">
                    <a href="{{route('user.create')}}" class="btn btn-primary"><i class="fas fa-plus"></i> {{trans('language.add_new')}}</a>
                </div>
            </div>
            <div id="collapseFilter" class="collapse">
                <div class="card mb-3">
                    <div class="card-body border-0">
                        @include('user.partials.form-filter')
                    </div>
                </div>
            </div>
            @if($is_filter)
                <div class="mb-2">
                    {{ trans('language.filter_mode') }}: {!! $is_filter !!}
                </div>
            @endif
            <div class="table-list-data">
                @include('user.partials.list-users')
            </div>
        </div>
    </section>
@stop

@section('js_library')
    @include('partials.script-library', ['datepicker' => true, 'fancybox' => true, 'select2' => true])
@stop

@section('js_page')
@stop