//
// Plugin: Full Calendar
//

// Buttons
.fc-button {
  background: $gray-100;
  background-image: none;
  border-bottom-color: #ddd;
  border-color: #ddd;
  color: $gray-700;

  &:hover,
  &:active,
  &.hover {
    background-color: #e9e9e9;
  }
}

// Calendar title
.fc-header-title h2 {
  color: #666;
  font-size: 15px;
  line-height: 1.6em;
  margin-left: 10px;
}

.fc-header-right {
  padding-right: 10px;
}

.fc-header-left {
  padding-left: 10px;
}

// Calendar table header cells
.fc-widget-header {
  background: #fafafa;
}

.fc-grid {
  border: 0;
  width: 100%;
}

.fc-widget-header:first-of-type,
.fc-widget-content:first-of-type {
  border-left: 0;
  border-right: 0;
}

.fc-widget-header:last-of-type,
.fc-widget-content:last-of-type {
  border-right: 0;
}

.fc-toolbar,
.fc-toolbar.fc-header-toolbar {
  margin: 0;
  padding: 1rem;
}

@include media-breakpoint-down(xs) {
  .fc-toolbar {
    flex-direction: column;

    .fc-left {
      order: 1;
      margin-bottom: .5rem;
    }

    .fc-center {
      order: 0;
      margin-bottom: .375rem;
    }

    .fc-right {
      order: 2;
    }
  }
}

.fc-day-number {
  font-size: 20px;
  font-weight: 300;
  padding-right: 10px;
}

.fc-color-picker {
  list-style: none;
  margin: 0;
  padding: 0;

  > li {
    float: left;
    font-size: 30px;
    line-height: 30px;
    margin-right: 5px;

    .fa,
    .fas,
    .far,
    .fab,
    .fal,
    .fad,
    .svg-inline--fa,
    .ion {
      transition: transform linear .3s;

      &:hover {
        @include rotate(30deg);
      }
    }
  }
}

#add-new-event {
  transition: all linear .3s;
}

.external-event {
  @include box-shadow($card-shadow);

  border-radius: $border-radius;
  cursor: move;
  font-weight: 700;
  margin-bottom: 4px;
  padding: 5px 10px;

  &:hover {
    @include box-shadow(inset 0 0 90px rgba(0, 0, 0, 0.2));
  }
}

.fc{
  .fc-daygrid-day-number{
    font-size: 20px;
  }
  //.fc-scroller{
  //  overflow: initial !important;
  //}
  //.fc-scroller-harness{
  //  overflow: initial;
  //}
  a{
    &:not(href){
      color: inherit;
    }
    &[data-toggle="dropdown"]:hover + .dropdown-menu{
      display: block;
    }
  }
  .fc-toolbar.fc-header-toolbar{
    margin-bottom: 0;
  }

  .fc-h-event{
    padding-left: 5px;
    padding-right: 5px;
    text-overflow: ellipsis;
    height: 3px;
    &:focus{
      outline: none;
    }
    .fc-event-main{
      display: none;
    }
  }

  .fc-daygrid-event-dropdown{
    padding: 10px;
    ul{
      font-size: 12px;
      margin: 0;
      padding-left: 15px;
    }
    p{
      margin-bottom: 5px;
    }
    &:hover{
      display: block;
    }
  }
  .fc-daygrid-day-top{
    flex-direction: row;
  }
  .fc-day-sat,
  .fc-day-sun{
    background: #fdfdfd;
  }
}
.calendar-title{
  position: relative;
  top: -20px;
  max-width: 100%;
  display: inline-block;
  overflow: hidden;
}
.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end), .fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){
  margin-top: 5px;
}
.fc-direction-ltr .fc-daygrid-event.fc-event-end, .fc-direction-rtl .fc-daygrid-event.fc-event-start{
  margin-top: 18px;
}
.fc .fc-scroller-harness{
  overflow: unset;
}
.fc-h-event {
    background-color: #0791a3;
    border-color: #0791a3;
}
.fc-col-header,
.fc-daygrid-body,
.fc-scrollgrid-sync-table,
.fc-timegrid-body,
.fc-timegrid-body table {
    width: 100% !important;
}