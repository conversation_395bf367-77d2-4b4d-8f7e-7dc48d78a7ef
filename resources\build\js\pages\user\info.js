$(document).ready(function(){
    $('#social_insurance_fee').keyup(function(event) {
        // skip for arrow keys
        if(event.which >= 37 && event.which <= 40) return;
        // format number
        $(this).val(function(index, value) {
            return value
                .replace(/\D/g, "")
                .replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        });
    });
    $("[name=first_name],[name=last_name]").keyup(function(){
        let firstName = $('[name=first_name]').val();
        let lastName = $('[name=last_name]').val();
        handleFirstNameAndLastName(firstName, lastName);
    });
    $("[name=first_name],[name=last_name]").change(function(){
       let firstName = $('[name=first_name]').val();
       let lastName = $('[name=last_name]').val();
       handleFirstNameAndLastName(firstName, lastName);
       lastName = lastName.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/gi, '');
       firstName = firstName.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/gi, '');
       $('[name=last_name]').val(lastName);
       $('[name=first_name]').val(firstName);

       if (!firstName || !lastName){
           $('[name=email]').val('')
           return
       }
        if (/^\s+$/.test(firstName) || /^\s+$/.test(lastName))
        {
            $('[name=email]').val('')
            return
        }
       let url = $("[name='email']").data('url');
        $.ajax({
            url: url,
            method: 'GET',
            dataType: "JSON",
            data: {
                firstName:firstName,
                lastName:lastName,
            },
            success: function (data){
                $('[name=email]').val(data.emailSuggest)
            },
            error: function (err){
                console.log(err);
            }
        });
    });
    $("#togglePw").click(function() {
        var passwordField = $("#pw");
        var fieldType = passwordField.attr("type") === "password" ? "text" : "password";
        if (fieldType === 'password') {
            $('#togglePw .input-group-text').html('<i class="fas fa-eye"></i>');
        } else {
            $('#togglePw .input-group-text').html('<i class="fas fa-eye-slash"></i>');
        }
        passwordField.attr("type", fieldType);
    });
    $("#generatePw").click(function() {
        var length = 16;
        var charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
        var password = "";
        for (var i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        $("#pw").val(password);
    });
    $('#showPasswordField').change(function () {
        if ($(this).is(':checked')) {
            $('#passwordSection').slideDown();
        } else {
            $('#passwordSection').slideUp(); 
        }
    });
});

function handleFirstNameAndLastName(firstName, lastName){
    lastName = lastName.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/gi, '');
    firstName = firstName.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/gi, '');
    $('[name=last_name]').val(lastName);
    $('[name=first_name]').val(firstName);
}