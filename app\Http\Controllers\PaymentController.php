<?php

namespace App\Http\Controllers;

use App\Enums\OrderStatus;
use App\Enums\PaymentChannel;
use App\Http\Requests\ReturnCallbackRequest;
use App\Http\Requests\StartPaymentRequest;
use App\Models\Order;
use App\Models\Payment;
use App\Services\Payment\PaymentServiceFactory;
use Symfony\Component\HttpFoundation\Response;

class PaymentController extends Controller
{
    /**
     * Start a new payment attempt for the given order.
     *
     * @param StartPaymentRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function start(StartPaymentRequest $request)
    {
        $order = Order::findOrFail($request->order_id);

        // Block if already paid
        abort_if($order->status === OrderStatus::PAID, Response::HTTP_BAD_REQUEST, __('message.order_already_paid'));

        // If latest payment expired/failed, we'll create new attempt
        $service = PaymentServiceFactory::make($request->input('channel'));
        $payment = $service->initiate($order);

        return redirect()->away($service->buildRedirectUrl($payment));
    }

    /** 
     * Handle the return callback from the payment gateway.
     *
     * @param string $channel
     * @param ReturnCallbackRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function return(string $channel, ReturnCallbackRequest $request)
    {
        $service = PaymentServiceFactory::make(PaymentChannel::from($channel));
        $payment = $service->handleReturn($request);

        // Redirect to your result page
        return redirect()->route('payments.result', ['payment' => $payment->id]);
    }

    /**
     * Handle the IPN (Instant Payment Notification) callback from the payment gateway.
     *
     * @param string $channel
     * @param ReturnCallbackRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ipn(string $channel, ReturnCallbackRequest $request)
    {
        $service = PaymentServiceFactory::make(PaymentChannel::from($channel));
        $payment = $service->handleIpn($request);

        return response()->json(['ok' => true, 'status' => $payment->status->value]);
    }

    /**
     * Show the payment result page.
     *
     * @param Payment $payment
     * @return \Illuminate\View\View
     */
    public function result(Payment $payment)
    {
        return view('payments.result', ['payment' => $payment]);
    }

    /**
     * Retry a payment for the given order.
     *
     * @param StartPaymentRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function retry(StartPaymentRequest $request)
    {
        $order   = Order::findOrFail($request->order_id);
        $service = PaymentServiceFactory::make($request->input('channel'));

        // business rule: allow retry only if order not paid
        abort_if($order->status->value === 'paid', Response::HTTP_BAD_REQUEST, __('message.order_already_paid'));

        $payment = $service->initiate($order);
        return redirect()->away($service->buildRedirectUrl($payment));
    }
}
