<?php

use App\Services\SmsService;
use App\Jobs\SendSmsJob;
use Illuminate\Support\Facades\Route;

// Test routes for SMS functionality
Route::prefix('test-sms')->group(function () {
    
    // Test synchronous OTP sending
    Route::get('/send-otp-sync/{phone}', function (string $phone, SmsService $smsService) {
        try {
            $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
            
            $response = $smsService->sendOtp($otp, [$phone]);
            
            return response()->json([
                'success' => true,
                'otp' => $otp,
                'phone' => $phone,
                'response' => $response,
                'attempt_info' => $smsService->getOtpAttemptInfo($phone)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'phone' => $phone,
                'attempt_info' => $smsService->getOtpAttemptInfo($phone)
            ], 400);
        }
    });

    // Test asynchronous OTP sending
    Route::get('/send-otp-async/{phone}', function (string $phone, SmsService $smsService) {
        try {
            $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
            
            // Check if can send before dispatching job
            if (!$smsService->getOtpAttemptInfo($phone)['can_send']) {
                throw new \Exception("OTP limit exceeded for phone: $phone");
            }
            
            $smsService->sendOtpAsync($otp, [$phone]);
            
            return response()->json([
                'success' => true,
                'message' => 'OTP job dispatched',
                'otp' => $otp,
                'phone' => $phone,
                'attempt_info' => $smsService->getOtpAttemptInfo($phone)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'phone' => $phone,
                'attempt_info' => $smsService->getOtpAttemptInfo($phone)
            ], 400);
        }
    });

    // Check OTP attempt info
    Route::get('/otp-info/{phone}', function (string $phone, SmsService $smsService) {
        return response()->json([
            'attempt_info' => $smsService->getOtpAttemptInfo($phone)
        ]);
    });

    // Test bulk SMS sending
    Route::post('/send-bulk', function (SmsService $smsService) {
        $phones = request()->input('phones', []); // Array of phone numbers
        $message = request()->input('message', 'Test message');
        
        if (empty($phones)) {
            return response()->json(['error' => 'No phones provided'], 400);
        }

        // Send via job for rate limiting
        $smsService->sendAsync(
            templateId: '114050793', // Use your template ID
            params: [
                [
                    "NUM" => "1",
                    "CONTENT" => $message
                ]
            ],
            phones: $phones,
            reqIdPrefix: 'BULK'
        );

        return response()->json([
            'success' => true,
            'message' => 'Bulk SMS job dispatched',
            'phone_count' => count($phones)
        ]);
    });
});
