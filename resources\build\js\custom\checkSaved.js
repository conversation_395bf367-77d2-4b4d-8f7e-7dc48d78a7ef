import locales from "../locales/locales";
import Swal from "sweetalert2";

let language = $("body").data('locales'),
    trans = locales(language);

export default function checkSaved() {
    let checkSaved = true,
    checkDateInput = false,
    checkedSavedModal = true;  
    $(document).on("input", "form:not([method=GET]) input:not('.pin-project'), form:not([method=GET]) select, form:not([method=GET]) textarea, form:not([method=GET]) div.note-editable", function(e){
        if (!$(this).closest(".modal").length) {
            checkSaved = false; 
        }
    });
    $(document).on("input", ".modal input[type='radio'], .modal select[name='parent']", function(e){
        checkSaved = true; 
    });
    $("form").on("submit", function() {
        window.onbeforeunload = null;
    })
    $("button[type='submit']").on("click", function() {
        window.onbeforeunload = null;
    })
    $(".change-timekeeping__submit").on("click", function() {
        checkSaved = true;
    })
    window.onbeforeunload = function(e) {
        if (!checkSaved || !checkedSavedModal){
            return 1;
        }
    };
    window.onpageshow = function(event) {
        if (event.persisted) {
            window.location.reload() 
        }
    }; 
    //check save modal
    $(document).on("input", ".modal input, .modal select, .modal textarea, .modal div.note-editable", function(e){
        checkedSavedModal = false;
    });
    $(document).on('input','.modal .start_at, .modal .end_at, .modal #title-task-kanban, .modal #estimate-task-kanban, .modal .note-editable.card-block', function(){
        checkedSavedModal = true;
    })
    $(document).on("input", ".modal input[type='radio'], .modal select[name='parent']", function(e){
        checkedSavedModal = true;
    });
    $(".modal button[type='submit']").on("click", function() {
        checkedSavedModal = true;
    })
    $(".modal #select-member, .modal #select-position").on("click", function() {
        checkedSavedModal = true;
    })
    $(".modal #created_at, .modal .input-check-focus, .modal #created_at-edit").on('focus', function(ev){
        checkDateInput = true;
    });
    $(document).on('change','.modal #start_at, .modal #ended_at', function(){
        checkDateInput = true;
    })
    $(document).on('input', '.modal #user_id-task-kanban, .modal #key_member-task-kanban', function(){
        checkDateInput = true;
    })
    $(".modal #created_at, .modal .input-check-focus, .modal #created_at-edit").on('change', function(ev){
        checkedSavedModal = false;
        checkDateInput = false;
    });
    $('.modal').on('hide.bs.modal', function (e) {
        if (!checkedSavedModal && (!checkDateInput)){
            e.preventDefault();
            Swal.fire({
                text: trans.confirm_exit,
                showCancelButton: true,
                confirmButtonText: trans.yes,
                cancelButtonText: trans.no,
                width: '27rem',
                customClass: "swal-custom"
              }).then((result) => {
                if (result.isConfirmed) {
                    checkedSavedModal = true;
                    $('.modal').modal('hide');
                }
            })
        }
    });
}
