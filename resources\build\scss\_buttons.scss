//
// Component: Button
//
@media (max-width: 1240px) {
    .full {
        width: 100%;
        min-width: auto;
    }
}

.modal-color:hover {
    opacity: 40%;
}

.d-flex.align-items-end.flex-wrap.mt-3.justify-content-between {
    margin-top: 0.5rem !important;
}

.btn:not(.note-btn) { 
    height: 50px;
    &.disabled,
    &:disabled {
        cursor: not-allowed;
    }
    // Flat buttons
    &.btn-flat {
        @include border-radius(0);
        border-width: 1px;
        box-shadow: none;
    }
    // input file btn
    &.btn-file {
        overflow: hidden;
        position: relative;
        >input[type="file"] {
            background-color: $white;
            cursor: inherit;
            display: block;
            font-size: 100px;
            min-height: 100%;
            min-width: 100%;
            opacity: 0;
            outline: none;
            position: absolute;
            right: 0;
            text-align: right;
            top: 0;
        }
    }
    align-content: center;
    .text-sm & {
        font-size: $font-size-sm !important;
    }
}

// Button color variations
.btn-default {
    background-color: #e1e1e1;
    border-color: #e1e1e1;
    color: $button-default-color;
    &:hover,
    &:active,
    &.hover {
        background-color: #FAFAFA;
        color: #000000;
        border-color: #DDDFE1;
    }
}

// Application buttons
.btn-app {
    @include border-radius(3px);
    background-color: $button-default-background-color;
    border: 1px solid $button-default-border-color;
    color: $gray-600;
    font-size: 12px;
    height: 60px;
    margin: 0 0 10px 10px;
    min-width: 80px;
    padding: 15px 5px;
    position: relative;
    text-align: center;
    // Icons within the btn
    >.fa,
    >.fas,
    >.far,
    >.fab,
    >.fal,
    >.fad,
    >.svg-inline--fa,
    >.ion {
        display: block;
        font-size: 20px;
    }
    >.svg-inline--fa {
        margin: 0 auto;
    }
    &:hover {
        background-color: $button-default-background-color;
        border-color: darken($button-default-border-color, 20%);
        color: $button-default-color;
    }
    &:active,
    &:focus {
        @include box-shadow(inset 0 3px 5px rgba($black, .125));
    }
    // The badge
    >.badge {
        font-size: 10px;
        font-weight: 400;
        position: absolute;
        right: -10px;
        top: -3px;
    }
}

// Extra Button Size
.btn-xs {
    @include button-size($button-padding-y-xs, $button-padding-x-xs, $button-font-size-xs, $button-line-height-xs, $button-border-radius-xs);
}

.dark-mode {
    .btn-default,
    .btn-app {
        background-color: lighten($dark, 2.5%);
        color: $white;
        border-color: $gray-600;
        &:hover,
        &:focus {
            background-color: lighten($dark, 5%);
            color: $gray-300;
            border-color: lighten($gray-600, 2.5%);
        }
    }
    .btn-light {
        background-color: lighten($dark, 7.5%);
        color: $white;
        border-color: $gray-600;
        &:hover,
        &:focus {
            background-color: black;
            /* Màu nền khi di chuột vào */
            color: white;
            /* Màu chữ khi di chuột vào */
            border-color: black;
        }
    }
}

.button-filter {
    height: 52px !important;
}

.calendar-current {
    background-color: #0791A3 !important;
    border: 0;
    &:hover,
    &:focus {
        background-color: #088493 !important;
    }
}

.calendar-prev,
.calendar-next {
    background-color: #FFFFFF !important;
    border: 1px solid #DDDFE1 !important;
}

.calendar-prev:hover,
.calendar-prev:focus,
.calendar-next:hover,
.calendar-next:focus {
    background-color: #FAFAFA !important;
    color: #000000;
}

.btn-primary.focus,
.btn-primary:focus,
.btn-primary:hover,
.btn-primary:active {
    background-color: #088493;
    border-color: #088493;
    color: #fff;
}