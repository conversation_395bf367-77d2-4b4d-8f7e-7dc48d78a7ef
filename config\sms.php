<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SMS Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | Configure rate limits for SMS sending to prevent abuse and manage costs.
    |
    */

    'rate_limits' => [
        // General SMS rate limit (messages per second)
        'messages_per_second' => env('SMS_RATE_LIMIT_PER_SECOND', 25),
        
        // OTP specific limits
        'otp' => [
            'max_attempts_per_phone_per_day' => env('SMS_OTP_MAX_DAILY', 5),
            'max_attempts_per_phone_per_hour' => env('SMS_OTP_MAX_HOURLY', 3),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Configure which queues to use for different types of SMS.
    |
    */

    'queues' => [
        'normal' => env('SMS_QUEUE_NORMAL', 'sms-normal'),
        'bulk' => env('SMS_QUEUE_BULK', 'sms-bulk'),
        'bulk_threshold' => env('SMS_BULK_THRESHOLD', 10), // Phone count threshold for bulk queue
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging levels and what to log.
    |
    */

    'logging' => [
        'log_all_requests' => env('SMS_LOG_ALL_REQUESTS', true),
        'log_payloads' => env('SMS_LOG_PAYLOADS', false), // Set to true for debugging
        'log_responses' => env('SMS_LOG_RESPONSES', true),
    ],
];
