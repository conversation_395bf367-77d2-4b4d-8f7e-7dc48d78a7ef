import constants from '../constants'
let language = $("body").data('locales');

export let langSummernote = (language) => {
    let lang;
    switch (language){
        case 'en':
            lang = 'es-ES'
            break;
        case 'ja':
            lang = 'ja-JP'
            break;
        default:
            lang = 'vi-VN'
    }

    return lang;
}

export function summernote(selector){
    if ($(selector).length){
        $(selector).summernote({
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link']],
                ['view', ['help']]
              ],
            height: 200,
            lang: langSummernote(language),
            styleTags: [
                'p',
                    { title: 'Blockquote', tag: 'blockquote', className: 'blockquote', value: 'blockquote' },
                    'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
                ],
            callbacks: {
                onImageUpload: function(images) {
                    if ($(selector).data('id') == 'coding-contest') {
                        sendFile(images[0]);
                    }
                }
            },
            codeviewFilter: true,
            codeviewIframeFilter: true,
            inheritPlaceholder: true,
        });
    }
}
function sendFile(file) {
    var data = new FormData();
    data.append("file", file);//You can append as many data as you want. Check mozilla docs for this
    $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: data,
        type: "POST",
        url: '/files/uploadTemp',
        cache: false,
        contentType: false,
        processData: false,
        success: function(response) {
            let file_name = response.data['file_path'].split('/');
            let url   = window.location.origin + '/tmp/' + file_name[1];
            $(".summernote").summernote("insertImage", url);
            let val = $("#imgContent").val();
            if (val == '') {
                $("#imgContent").val(file_name[1]);
            } else {
                let nVal = val + constants.GROUP_CONCAT_SEPARATOR + file_name[1];
                $("#imgContent").val(nVal);
            }
        }
    });
}