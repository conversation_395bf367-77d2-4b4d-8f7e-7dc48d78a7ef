<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\CheckVersionController;
use App\Http\Controllers\Api\OrderController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api', 'as'=>'api.'], function () {
    Route::group(['prefix' => 'auth', 'as'=>'auth.'], function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:api');
        Route::post('refresh', [AuthController::class, 'refresh'])->middleware('auth:api');
    });

    // Check version
    Route::post('check-version', [CheckVersionController::class, 'checkVersion']);

    Route::group(['middleware' => 'auth:api'], function () {
        // Payment
        Route::post('/payments/start', [PaymentController::class, 'start']);

        // Order
        Route::post('/orders', [OrderController::class, 'store']);
        // Version
        Route::get('test-middleware', function () {
            return response()->json([
                'message' => 'Test middleware success'
            ]);
        });
    });
    Route::post('send-otp', function () {
        $smsService = new \App\Services\SmsService();
        $response = $smsService->sendOtp('123456', ['84354091999']);
        return response()->json($response);
    });
});
