<?php

namespace App\Services\Payment;

use App\Contracts\PaymentService;
use App\Enums\PaymentChannel;
use App\Enums\PaymentStatus;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class MomoService extends BasePaymentService implements PaymentService
{
    public function channel(): PaymentChannel
    {
        return PaymentChannel::MOMO;
    }

    /**
     * Initiate a new payment for the given order.
     * - Reuse existing pending payment if valid
     * - Create a new payment record with MoMo metadata
     * - Call MoMo /create API to get payUrl/deeplink
     */
    public function initiate(Order $order, array $options = []): Payment
    {
        // Reuse pending payment if still valid
        $existing = $order->payments()
            ->where('channel', PaymentChannel::MOMO->value)
            ->where('status', PaymentStatus::PENDING->value)
            ->where(function ($q) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>', now());
            })
            ->latest()
            ->first();

        if ($existing) {
            // If payUrl is missing (e.g. previous attempt failed before storing), we will re-call create.
            if (!empty($existing->meta['payUrl'])) {
                $this->log($existing, 'info', 'Reusing existing pending payment', []);
                return $existing;
            }
        }

        // Compose local metadata
        $expiresAt = now()->addMinutes(Config::get('payments.momo.expire_minutes', 15));
        $meta = [
            'orderId'   => $order->code . '-' . uniqid(),
            'requestId' => (string) Str::uuid(),
            'returnUrl' => route('payments.return', ['channel' => 'momo']),
            'notifyUrl' => route('payments.ipn', ['channel' => 'momo']),
        ];

        // Create local payment record first (PENDING)
        $payment = $this->createPayment($order, PaymentChannel::MOMO->value, $expiresAt, $meta);

        // Call MoMo /create to get payUrl
        $m = $this->callCreateApi($payment);

        // Persist MoMo response
        $merged = array_merge($payment->meta ?? [], [
            'payUrl'    => $m['payUrl']    ?? null,
            'deeplink'  => $m['deeplink']  ?? ($m['deeplink_web_inapp'] ?? null),
            'qrCodeUrl' => $m['qrCodeUrl'] ?? null,
            'raw_create_response' => $m,
        ]);
        $payment->update(['meta' => $merged]);

        return $payment->fresh('order');
    }

    /**
     * Build the redirect URL for the payment.
     * - Use payUrl if available, otherwise fallback to home page.
     */
    public function buildRedirectUrl(Payment $payment): string
    {
        return $payment->meta['payUrl'] ?? url('/'); // Fallback: home if payUrl is missing
    }

    /**
     * Handle browser return from MoMo.
     * - Verify signature
     * - Record transaction for audit
     * - Update payment meta with return status hint
     * - Return the payment object (fresh with order)
     */
    public function handleReturn(Request $request): Payment
    {
        $payment = $this->findPaymentByOrderId((string) $request->input('orderId'));
        $valid   = $this->verifyCallbackSignature($request->all());

        $payload = $request->all() + ['signature_valid' => $valid, 'source' => 'return'];

        // Always record a transaction for audit
        $this->tx(
            $payment,
            'return',
            \App\Enums\TransactionStatus::CALLBACK->value,
            (string) $request->input('resultCode'),
            'Browser return',
            ['payload' => $payload, 'signature_valid' => $valid]
        );

        // If invalid signature, mark failed (but do not pay)
        if (!$valid) {
            return $this->markFailed($payment, 'INVALID_SIGN', 'Invalid signature (return)', $payload, 'return');
        }

        // Hint for UI; keep status pending until IPN confirms
        $meta = $payment->meta ?? [];
        $meta['return_seen'] = true;
        $meta['return_success_hint'] = $this->isSuccessCode($request);
        $payment->update(['meta' => $meta]);

        return $payment->fresh('order');
    }

    /**
     * Handle browser return from MoMo.
     * - Verify signature
     * - Record transaction for audit
     * - Update payment meta with return status hint
     * - Return the payment object (fresh with order)
     */
    public function handleIpn(Request $request): Payment
    {
        $payment = $this->findPaymentByOrderId((string) $request->input('orderId'));
        $valid   = $this->verifyCallbackSignature($request->all());
        $payload = $request->all() + ['signature_valid' => $valid, 'source' => 'ipn'];

        $providerTxnId = (string) $request->input('transId'); // MoMo transaction id (if present)
        $payment = $this->ensureIdempotentByProviderTxn($providerTxnId, $payment);

        if (!$valid) {
            return $this->markFailed($payment, 'INVALID_SIGN', 'Invalid signature (ipn)', $payload, 'ipn');
        }

        // Even if our local payment expired, if provider confirms success we still honor success to avoid losing user's money
        if ($this->isSuccessCode($request)) {
            return $this->markSuccess($payment, ['code' => '0', 'message' => 'Success'] + $payload, $providerTxnId, 'ipn');
        }

        return $this->markFailed($payment, (string) $request->input('resultCode'), 'MoMo failed (ipn)', $payload, 'ipn');
    }

    /**
     * Query MoMo for payment status.
     * - Uses MoMo /query API
     * - Updates payment status based on response
     * - Returns the updated payment object (fresh with order)
     */
    public function query(Payment $payment): Payment
    {
        // Example skeleton (commented):
        // $cfg = config('payments.momo');
        // $body = [
        //     'partnerCode' => $cfg['partner_code'],
        //     'accessKey'   => $cfg['access_key'],
        //     'requestId'   => (string) Str::uuid(),
        //     'orderId'     => $payment->meta['orderId'] ?? '',
        //     'lang'        => 'vi',
        // ];
        // $body['signature'] = $this->signRaw($this->buildRawSignatureQuery($body), $cfg['secret_key']);
        // $res = Http::asJson()->post(rtrim($cfg['endpoint'], '/').'/query', $body)->json();
        // if (($res['resultCode'] ?? null) === 0) { $this->markSuccess($payment, ['code'=>'0','message'=>'Success'] + $res, $res['transId'] ?? null, 'query'); }
        // else { $this->markFailed($payment, (string)($res['resultCode'] ?? 'NA'), 'MoMo failed (query)', $res, 'query'); }
        return $payment->refresh();
    }

    /**
     * Call MoMo /create API to initiate a new payment.
     * - Returns the JSON response from MoMo
     * - Records the transaction for audit
     * - Handles errors and retries
     */
    protected function callCreateApi(Payment $payment): array
    {
        $cfg = config('payments.momo');

        $body = [
            'partnerCode' => $cfg['partner_code'],
            'accessKey'   => $cfg['access_key'],
            'requestId'   => $payment->meta['requestId'],
            'amount'      => $payment->amount,
            'orderId'     => $payment->meta['orderId'],
            'orderInfo'   => 'Payment for order ' . $payment->order->code,
            'returnUrl'   => $payment->meta['returnUrl'],
            'notifyUrl'   => $payment->meta['notifyUrl'],
            'extraData'   => '', // optional base64 string
            'requestType' => $cfg['request_type'] ?? 'captureWallet', // or 'payWithATM'
            // Optional: 'lang' => 'vi',
        ];

        // Signature per MoMo v2 (ordered by MoMo's spec)
        $raw = $this->buildRawSignatureCreate($body);
        $body['signature'] = $this->signRaw($raw, $cfg['secret_key']);

        $resp = Http::asJson()
            ->timeout(20)
            ->post(rtrim($cfg['endpoint'], '/') . '/create', $body);

        if (!$resp->ok()) {
            // Persist a transaction record for troubleshooting
            $this->tx($payment, 'create', \App\Enums\TransactionStatus::FAILED->value, 'HTTP_' . strval($resp->status()), 'HTTP error during MoMo create', [
                'request'  => $body,
                'response' => ['text' => $resp->body()],
                'signature_valid' => true,
            ]);
            throw new \RuntimeException('MoMo create API failed with HTTP status ' . $resp->status());
        }

        $json = $resp->json();

        // Record the create transaction
        $this->tx($payment, 'create', \App\Enums\TransactionStatus::CALLBACK->value, (string)($json['resultCode'] ?? ''), 'MoMo create response', [
            'request'  => $body,
            'response' => $json,
            'signature_valid' => true,
        ]);

        // Basic sanity check
        if (($json['resultCode'] ?? null) !== 0) {
            // Keep payment pending so user can retry; do not mark failed at create stage
            $this->log($payment, 'warning', 'MoMo create returned non-zero resultCode', ['json' => $json]);
        }

        return $json ?? [];
    }

    /**
     * Verify the callback signature from MoMo.
     * - Uses the raw signature string built from callback parameters
     * - Returns true if signature matches, false otherwise
     */
    protected function verifyCallbackSignature(array $params): bool
    {
        $signature = $params['signature'] ?? null;
        if (!$signature) {
            // Some flows may return different key name; adjust if necessary
            return false;
        }
        $raw = $this->buildRawSignatureCallback($params);
        $calc = $this->signRaw($raw, (string) config('payments.momo.secret_key'));
        return hash_equals($calc, (string) $signature);
    }

    /**
     * Build raw signature for create request.
     * Common v2 fields: partnerCode&accessKey&requestId&amount&orderId&orderInfo&returnUrl&notifyUrl&extraData&requestType
     * Some fields may be absent depending on product; missing keys treated as empty strings.
     */
    protected function buildRawSignatureCreate(array $p): string
    {
        // NOTE: MoMo docs show different orders in different samples; we keep a canonical set and sort accordingly
        // to ensure determinism. Always double-check with your MoMo environment and adjust the order if needed.
        $ordered = [
            'partnerCode' => $p['partnerCode'] ?? '',
            'accessKey'   => $p['accessKey']   ?? '',
            'requestId'   => $p['requestId']   ?? '',
            'amount'      => $p['amount']      ?? '',
            'orderId'     => $p['orderId']     ?? '',
            'orderInfo'   => $p['orderInfo']   ?? '',
            'returnUrl'   => $p['returnUrl']   ?? '',
            'notifyUrl'   => $p['notifyUrl']   ?? '',
            'extraData'   => $p['extraData']   ?? '',
            'requestType' => $p['requestType'] ?? '',
        ];
        return $this->joinPairs($ordered);
    }

    /**
     * Build raw signature for callback verification.
     * Common v2 fields: partnerCode&accessKey&requestId&amount&orderId&orderInfo&orderType&transId&message
     * &localMessage&responseTime&errorCode/payType&extraData
     * Some fields may be absent depending on product; missing keys treated as empty strings.
     */
    protected function buildRawSignatureCallback(array $p): string
    {
        $ordered = [
            'partnerCode'  => $p['partnerCode']  ?? '',
            'accessKey'    => $p['accessKey']    ?? '',
            'requestId'    => $p['requestId']    ?? '',
            'amount'       => $p['amount']       ?? '',
            'orderId'      => $p['orderId']      ?? '',
            'orderInfo'    => $p['orderInfo']    ?? '',
            'orderType'    => $p['orderType']    ?? '',
            'transId'      => $p['transId']      ?? '',
            'message'      => $p['message']      ?? '',
            'localMessage' => $p['localMessage'] ?? '',
            'responseTime' => $p['responseTime'] ?? '',
            'errorCode'    => $p['errorCode']    ?? ($p['resultCode'] ?? ''),
            'payType'      => $p['payType']      ?? '',
            'extraData'    => $p['extraData']    ?? '',
        ];
        return $this->joinPairs($ordered);
    }

    /**
     * Sign a raw string using HMAC-SHA256 with the provided secret.
     * - Returns the hex-encoded signature
     */
    protected function signRaw(string $raw, string $secret): string
    {
        return hash_hmac('sha256', $raw, $secret);
    }

    /**
     * Join key-value pairs into a query string format.
     * - Keys and values are URL-encoded
     * - Pairs are sorted by keys to ensure deterministic order
     */
    protected function joinPairs(array $ordered): string
    {
        $pairs = [];
        foreach ($ordered as $k => $v) {
            $pairs[] = $k . '=' . $v;
        }
        return implode('&', $pairs);
    }

    /**
     * Check if the request indicates a successful payment.
     * - MoMo: resultCode == 0 means success
     * - Also checks errorCode for some flows
     */
    protected function isSuccessCode(Request $request): bool
    {
        // MoMo: resultCode == 0 means success
        return ((string) $request->input('resultCode')) === '0'
            || ((string) $request->input('errorCode')) === '0';
    }

    /**
     * Find a payment by its orderId.
     * - Throws ModelNotFoundException if not found
     * - Returns the Payment model
     */
    protected function findPaymentByOrderId(string $orderId): Payment
    {
        $payment = Payment::whereJsonContains('meta->orderId', $orderId)->latest()->first();
        if (!$payment) {
            throw new ModelNotFoundException("Payment not found by orderId={$orderId}");
        }
        return $payment;
    }

    /**
     * Get a human-readable message for the given result code.
     * - Maps MoMo result codes to user-friendly messages
     * - Returns null if code is not recognized
     */
    public function getResponseCodeMessage(?string $code): ?string
    {
        $map = [
            // Success / Pending
            '0'    => 'Success',
            '9000' => 'Authorized (1-step: success; 2-step: capture/cancel)',
            '7000' => 'Processing',
            '7002' => 'Processing by provider',
            '1000' => 'Initiated - waiting user confirmation',

            // Failures (final)
            '1001' => 'Insufficient funds',
            '1002' => 'Rejected by payment instrument issuer',
            '1003' => 'Canceled after authorized (timeout handlers)',
            '1004' => 'Exceeds daily/monthly limit',
            '1005' => 'URL/QR expired',
            '1006' => 'User denied confirmation',
            '1007' => 'Inactive/nonexistent user account',
            '1017' => 'Canceled by merchant',
            '1026' => 'Restricted by promotion rules',

            // System / config / format
            '10'   => 'System under maintenance',
            '11'   => 'Access denied - merchant settings',
            '12'   => 'Unsupported API version',
            '13'   => 'Merchant authentication failed',
            '20'   => 'Bad format request',
            '21'   => 'Invalid transaction amount',
            '22'   => 'Amount out of range',
            '40'   => 'Duplicated requestId',
            '41'   => 'Duplicated orderId',
            '42'   => 'Invalid orderId / not found',
            '43'   => 'Analogous transaction in process',
            '45'   => 'Duplicated itemId',
            '47'   => 'Inapplicable information',
            '98'   => 'QR code not generated successfully',
            '99'   => 'Unknown error',

            // Refund domain (subset)
            '1080' => 'Refund attempt failed, retry later',
            '1081' => 'Refund rejected - already refunded/exceeds amount',
            '1088' => 'Refund ineligible',
            '2019' => 'Invalid orderGroupId',

            // User account restrictions
            '4001' => 'User account restricted',
            '4002' => 'User KYC not verified (C06)',
            '4100' => 'User login failed',
        ];

        return $map[$code] ?? [PaymentStatus::FAILED, 'Unmapped MoMo resultCode'];
    }
}
