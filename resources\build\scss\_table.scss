//
// Component: Table
//
.table {
    &:not(.table-dark) {
        color: inherit;
    }
    // fixed table head
    &.table-head-fixed {
        thead tr:nth-child(1) th {
            background-color: $white;
            border-bottom: 0;
            box-shadow: inset 0 1px 0 $table-border-color, inset 0 -1px 0 $table-border-color;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        &.table-dark {
            thead tr {
                &:nth-child(1) th {
                    background-color: $table-dark-bg;
                    box-shadow: inset 0 1px 0 $table-dark-border-color, inset 0 -1px 0 $table-dark-border-color;
                }
            }
        }
    }
    // no border
    &.no-border {
        &,
        td,
        th {
            border: 0;
        }
    }
    // .text-center in tables
    &.text-center {
        &,
        td,
        th {
            text-align: center;
        }
    }
    &.table-valign-middle {
        thead>tr>th,
        thead>tr>td,
        tbody>tr>th,
        tbody>tr>td {
            vertical-align: middle;
        }
    }
    .card-body.p-0 & {
        thead>tr>th,
        thead>tr>td,
        tfoot>tr>th,
        tfoot>tr>td,
        tbody>tr>th,
        tbody>tr>td {
            &:first-of-type {
                padding-left: map-get($spacers, 4);
            }
            &:last-of-type {
                padding-right: map-get($spacers, 4);
            }
        }
    }
}

// Expandable Table
.table-hover tbody tr.expandable-body:hover {
    background-color: inherit !important;
}

[data-widget="expandable-table"] {
    cursor: pointer;
    i {
        transition: transform $transition-speed linear;
    }
    &[aria-expanded="true"] {
        td>i {
            // stylelint-disable selector-max-attribute
            &[class*="right"] {
                transform: rotate(90deg);
            }
            &[class*="left"] {
                transform: rotate(-90deg);
            }
            // stylelint-enable selector-max-attribute
        }
    }
}

.expandable-body {
    >td {
        padding: 0 !important;
        width: 100%;
        >div,
        >p {
            padding: $table-cell-padding;
        }
    }
    .table {
        width: calc(100% - #{$table-cell-padding});
        margin: 0 0 0 $table-cell-padding;
        tr:first-child {
            td,
            th {
                border-top: none;
            }
        }
    }
}

.dark-mode {
    .table-bordered {
        &,
        td,
        th {
            border-color: $gray-600;
        }
    }
    .table-hover {
        tbody tr:hover {
            color: $gray-300;
            background-color: lighten($dark, 2.5%);
            border-color: $gray-600;
        }
    }
    .table {
        thead th {
            border-bottom-color: $gray-600;
        }
        th,
        td {
            border-top-color: $gray-600;
        }
        &.table-head-fixed {
            thead tr:nth-child(1) th {
                background-color: lighten($dark, 5%);
            }
        }
    }
}

.table-responsive {
    overflow: auto;
    >.table tr th,
    >.table tr th a {
        // text-overflow: unset;
        overflow: hidden;
        white-space: break-spaces;
    }
}

.min-width-thead {
    min-width: 145px;
    max-width: 500px;
}

.table-list-data {
    padding-left: 15px;
    padding-right: 15px;
    margin-left: -15px;
    margin-right: -15px;
    position: relative;

    .table-responsive.table-projects {
        th.box-shadow {
            width: 0;
            max-width: 0;
            min-width: 0;
            padding: 0;
            visibility: hidden;
        }
        tbody tr {
            --height: 0;
            td {
                background-color: inherit;
            }
            .box-shadow {
                content: "";
                position: absolute !important;
                width: calc(100% - 30px);
                left: 15px;
                height: var(--height);
                border-radius: 12px;
                padding: 0;
                border: none;
                border-radius: 12px;
                background-color: transparent;
            }
            &:hover {
                box-shadow: unset !important;
                .box-shadow {
                    box-shadow: 3px 0.96px 30px #77777791;
                    z-index: 300;
                }
                td {
                    z-index: 500;
                }
            }
            &.modal-open{
                td {
                    z-index: unset!important;
                }
            }
        }
    }
}

.list-project-screen .table-projects.table-responsive {
    th.box-shadow {
        width: 0;
        max-width: 0;
        min-width: 0;
        padding: 0;
        visibility: hidden;
    }
    tbody tr {
        .box-shadow {
            content: "";
            position: absolute !important;
            width: calc(100% - 30px);
            left: 15px;
            @media (min-width: 1200px) {
                width: calc(100% - 30px);
                left: 15px;
            }
            height: var(--height);
            border-radius: 12px;
        }
        &:hover {
            box-shadow: unset !important;
            .box-shadow {
                box-shadow: 3px 0.96px 30px #77777791;
            }
        }
    }
}
.table-responsive>.table tr th, .table-responsive>.table tr th a { white-space: nowrap !important };
.table.table-valign-middle thead > tr > th, .table.table-valign-middle thead > tr > td{
    vertical-align: baseline;
}

.tooltip {
    z-index: 120000;
}