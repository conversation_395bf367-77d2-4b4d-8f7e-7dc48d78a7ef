import locales from "../locales/locales";

let language = $("body").data('locales'),
    trans = locales(language);

export function checkPasswordConfirmValid(){
    let password = document.getElementById("password"),
        confirm_password = document.getElementById("confirm_password");
    if (/\s/.test(password.value)) {
        password.setCustomValidity(trans.password_invalid);
    } else if(password.value !== confirm_password.value) {
        confirm_password.setCustomValidity(trans.confirm_password_does_not_match);
    } else {
        confirm_password.setCustomValidity('');
    }
}

export function checkTypeFileImage(that) {
    let fileExtension = ['png', 'gif', 'jpg', 'jpeg'];
    let fileExtentionCurrent = $(that).val().split('.').pop().toLowerCase();
    if ($.inArray(fileExtentionCurrent, fileExtension) == -1) {
        return false
    }else return true
}

export function readFileImage() {
    let that = this,
        $that = $(that),
        checkType = checkTypeFileImage(that),
        img = $('#' + $that.attr('id') + '_view'),
        imgDefault = $that.data('origin') ? $that.data('origin') : '/images/placeholder.png';
    if($that.val()){
        if (that.files && that.files[0]) {
            let reader = new FileReader();
            reader.readAsDataURL(that.files[0]);
            reader.onload = function (e) {
                if (checkType){
                    img.attr('src', e.target.result);
                }else {
                    img.attr('src', imgDefault);
                }
            };
        }
    }else {
        img.attr('src', imgDefault);
    }
}