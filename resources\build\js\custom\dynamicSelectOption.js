import {select2Base} from "../plugins/select2";

export default function dynamicSelectOption(){
    let select = $(this),
        url = select.data('url'),
        val = select.val(),
        child = select.data('child'),
        selectChild = $(child);
    selectChild.trigger('change');
    $.ajax({
        url: url,
        data: {
            id: val
        },
        method: 'GET',
        success: function (data){
            if (data.status === 200){
                selectChild.html('<option></option>');
                data.options.forEach((option, index) => {
                    selectChild.append(`<option value="${option.value}">${option.text}</option>`);
                });
                selectChild.select2('destroy');
                select2Base(child);
            }else {
                console.log(data.msg);
            }
        },
        error: function (err){
            console.log(err);
        }
    })

}