<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Http\RedirectResponse;

trait RedirectInvalidPage
{
    /**
     * Check if current page is out of range, redirect to valid page if needed.
     * 
     * @param  LengthAwarePaginator $paginator
     * @param  Request $request
     * @return RedirectResponse|null
     */
    protected function redirectInvalidPage(LengthAwarePaginator $paginator, Request $request): ?RedirectResponse
    {
        $currentPage = (int) $request->get('page', 1);

        // Last page check
        if ($paginator->lastPage() < $currentPage) {
            return redirect($request->fullUrlWithQuery(['page' => $paginator->lastPage()]));
        }
        // First page check
        if ($currentPage < 1) {
            return redirect($request->fullUrlWithQuery(['page' => 1]));
        }

        return null;
    }
}
