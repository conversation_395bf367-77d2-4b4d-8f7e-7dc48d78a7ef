name: Development deploy

on:
  push:
    branches:
      - develop*

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: SSH to remote server and run script to deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }} #hostname of server
          username: ${{ secrets.USERNAME }} #username login to server
          password: ${{ secrets.PASSWORD }} #password login to server
          port: ${{ secrets.PORT }} #port of server - often port is 22
          command_timeout: "30m"   # Increase command timeout to 30 minutes
          script: | # run with multiple script
            cd /var/www/beefarm.vn/htdocs
            git reset --hard
            echo ========== git fetch origin ==========
            git fetch origin
            CURRENT_BRANCH="origin/${{ github.ref_name }}"
            echo "========== Merging branch $CURRENT_BRANCH ========== "
            git merge --no-ff "$CURRENT_BRANCH" -m "Auto-merge $CURRENT_BRANCH"

            echo ========== composer install ==========
            composer install

            echo ========== npm install ==========
            npm install
            echo ========== npm run plugins ==========
            npm run plugins
            echo ========== npm run build ==========
            npm run build

            echo ========== sudo chown -R apache storage/ ==========
            sudo chown -R apache storage/

            echo ========== php artisan migrate ==========
            php artisan migrate

            echo ========== sudo systemctl restart supervisord ==========
            sudo systemctl restart supervisord
            echo ========== sudo systemctl restart crond ==========
            sudo systemctl restart crond

            echo "✅ Deploy done"
