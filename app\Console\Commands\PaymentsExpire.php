<?php

namespace App\Console\Commands;

use App\Enums\PaymentStatus;
use App\Models\Payment;
use App\Services\Payment\PaymentServiceFactory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PaymentsExpire extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire pending payments that passed their validity window';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $pending = Payment::where('status', PaymentStatus::PENDING->value)
            ->whereNotNull('expires_at')
            ->where('expires_at', '<', now())
            ->get();

        foreach ($pending as $payment) {
            $service = PaymentServiceFactory::make($payment->channel);
            $service->expire($payment);
            Log::info("Expired payment {$payment->id} ({$payment->channel->value})");
        }

        return self::SUCCESS;
    }
}
