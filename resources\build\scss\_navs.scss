//
// Component: Nav
//

.nav-pills {
  .nav-link {
    color: $gray-600;

    &:not(.active):hover {
      color: theme-color("primary");
    }
  }

  .nav-item {
    &.dropdown.show {
      .nav-link:hover {
        color: $dropdown-link-active-color;
      }
    }
  }
}

// Vertical Tabs
.nav-tabs.flex-column {
  border-bottom: 0;
  border-right: $nav-tabs-border-width solid $nav-tabs-border-color;

  .nav-link {
    border-bottom-left-radius: $nav-tabs-border-radius;
    border-top-right-radius: 0;
    margin-right: -$nav-tabs-border-width;

    @include hover-focus () {
      border-color: $gray-200 transparent $gray-200 $gray-200;
    }
  }

  .nav-link.active,
  .nav-item.show .nav-link {
    border-color: $gray-300 transparent $gray-300 $gray-300;
  }

  &.nav-tabs-right {
    border-left: $nav-tabs-border-width solid $nav-tabs-border-color;
    border-right: 0;

    .nav-link {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: $nav-tabs-border-radius;
      border-top-left-radius: 0;
      border-top-right-radius: $nav-tabs-border-radius;
      margin-left: -$nav-tabs-border-width;

      @include hover-focus () {
        border-color: $gray-200 $gray-200 $gray-200 transparent;
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      border-color: $gray-300 $gray-300 $gray-300 transparent;
    }
  }
}

.navbar-no-expand {
  flex-direction: row;

  .nav-link {
    padding-left: $navbar-nav-link-padding-x;
    padding-right: $navbar-nav-link-padding-x;
  }

  .dropdown-menu {
    position: absolute;
  }
}

// Color variants
@each $color, $value in $theme-colors {
  @if $color == dark or $color == light {
    .navbar-#{$color} {
      background-color: $value;
      @if $color == dark {
        border-color: lighten($dark, 10%);
      }
    }
  }
}

@each $color, $value in $theme-colors {
  @if $color != dark and $color != light {
    .navbar-#{$color} {
      background-color: $value;
    }
  }
}

@each $color, $value in $colors {
  .navbar-#{$color} {
    background-color: $value;
  }
}

.dark-mode {
  .nav-pills .nav-link {
    color: $gray-400;
  }
  .nav-tabs {
    border-color: lighten($dark, 15%);

    .nav-link:focus,
    .nav-link:hover {
      border-color: lighten($dark, 15%);
    }

    .nav-item.show .nav-link,
    .nav-link.active {
      background-color: $dark;
      border-color: lighten($dark, 15%) lighten($dark, 15%) transparent lighten($dark, 15%);
      color: $white;
    }

    &.flex-column {
      .nav-item.show .nav-link,
      .nav-link {
        &.active,
        &:focus,
        &:hover {
          border-color: lighten($dark, 15%) transparent lighten($dark, 15%) lighten($dark, 15%);
        }
        &:focus,
        &:hover {
          background-color: lighten($dark, 5%);
        }
      }
      &.nav-tabs-right {
        border-color: lighten($dark, 15%);
        .nav-link {
          &.active,
          &:focus,
          &:hover {
            border-color: lighten($dark, 15%) lighten($dark, 15%) lighten($dark, 15%) transparent;
          }
        }
      }
    }
  }
}

.nav-tabs.nav-tabs-menu{
  font-size: 13px;
  .nav-link{
    color: black;
    transition: all linear .2s;
    padding: 0.5rem 2rem;
    &.active, .nav-item.show &{
      color: black !important;
      background-color: transparent;
      border-color: #f4f6f900 #ffffff00 #FCCA33;
      border-bottom: 3px solid;
    }
  }
  .nav-link.active, .nav-item.show .nav-link{
    color: $bee;
    background-color: transparent;
    border-color: #f4f6f900 #f4f6f900 #0791A3;
  }
  .nav-link:not(.active):hover,
  .nav-link:not(.active):focus{
    background-color: #E9E9E9;
    color: $gray-900;
    border-radius: 0;
  }

  .dropdown-item{
    i{
      display: inline-block;
      width: 20px;
      text-align: center;
    }
  }

  @media (max-width: 1199px) {
    font-size: 14px;
    .nav-link{
      // display: none;
      &.active, .nav-item.show &{
        display: block;
      }
    }
  }
}
