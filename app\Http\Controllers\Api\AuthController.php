<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\UserDevice;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    // Login
    public function login(Request $request)
    {
        $userName = $request->input('email');
        $loginType = filter_var($userName, FILTER_VALIDATE_EMAIL) ? 'email' : 'phone';

        $credentials = [
            $loginType => $userName,
            'password' => $request->input('password')
        ];
        if (! $token = auth('api')->attempt($credentials)) {
            return response()->json(
                [
                    'code' => Response::HTTP_FORBIDDEN,
                    'message' => trans('message.error_login')
                ],
                Response::HTTP_FORBIDDEN
            );
        }

        $user = auth('api')->user();

        // Generate a refresh token
        $refreshToken = JWTAuth::claims(['refresh' => true])->fromUser(auth('api')->user());

        // Save token device
        if (isset($request->device_token)) {
            $this->deleteUserDevice($request->device_token);
            $userDevice = new UserDevice();
            $userDevice->user_id = $user->id;
            $userDevice->device_information = $request->device_information;
            $userDevice->device_token = $request->device_token;
            $userDevice->status = UserDevice::STATUS_ONLINE;
            $userDevice->last_connection = Carbon::now();
            $userDevice->save();
        }

        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => __('language.success'),
            'data' => [
                'access_token' => $token,
                'refresh_token' => $refreshToken,
                'user_id' => $user->id,
                'name' => $user->name,
                'language_id' => $user->language_id,
                'email' => $user->email,
                'phone' => $user->phone,
                // 'avatar' => route('api.v1.user.avatar', ['id' => $user->id]),
                'roles' => $user->roles->pluck('name'),
                'expires_in' => auth('api')->factory()->getTTL() * 60, // seconds
                // 'expires_at' => Carbon::parse(
                //     $tokenResult->token->expires_at
                // )->toDateTimeString(),
            ]
        ], Response::HTTP_OK);
    }

    // Logout
    public function logout()
    {
        auth('api')->logout();
        return response()->json(['message' => 'Successfully logged out']);
    }

    public function refresh(Request $request)
    {
        $refreshToken = $request->input('refresh_token') ?? $request->bearerToken();

        try {
            $payload = JWTAuth::setToken($refreshToken)->getPayload();
            if (!$payload->get('refresh')) {
                return response()->json(['error' => 'Invalid refresh token'], 401);
            }
            // Lấy user từ refresh token
            $user = JWTAuth::setToken($refreshToken)->toUser();
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }
            // Tạo access token mới với guard api
            $accessToken = auth('api')->login($user);
            // Tạo refresh token mới
            $newRefreshToken = JWTAuth::claims(['refresh' => true])->fromUser($user);

            return response()->json([
                'access_token' => $accessToken,
                'refresh_token' => $newRefreshToken,
                'token_type' => 'bearer',
                'expires_in' => auth('api')->factory()->getTTL() * 60
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid token'], 401);
        }
    }

    /**
     * Delete user device.
     * 
     * @param $deviceToken
     * @return void
     */
    private function deleteUserDevice($deviceToken)
    {
        UserDevice::where('user_id', Auth::id())
            ->where('device_token', $deviceToken)
            ->delete();
    }
}
