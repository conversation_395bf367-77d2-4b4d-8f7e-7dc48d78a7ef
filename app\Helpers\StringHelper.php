<?php


namespace App\Helpers;

use App\Models\Language;

class StringHelper
{
    /**
     * Remove all special character in string
     *
     * @param $string
     * @return string|string[]|null
     */
    public function cleanSpecialCharacters($string) {
        $string = str_replace(' ', '', $string); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }

    /**
     * Clean Vietnamese accents
     *
     * @param $str
     * @return string|string[]
     */
    public function cleanVietnameseAccents($str){

        $unicode = array(
            'a'=>'á|à|ả|ã|ạ|ă|ắ|ặ|ằ|ẳ|ẵ|â|ấ|ầ|ẩ|ẫ|ậ',
            'd'=>'đ',
            'e'=>'é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ',
            'i'=>'í|ì|ỉ|ĩ|ị',
            'o'=>'ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ',
            'u'=>'ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự',
            'y'=>'ý|ỳ|ỷ|ỹ|ỵ',
            'A'=>'Á|À|Ả|Ã|Ạ|Ă|Ắ|Ặ|Ằ|Ẳ|Ẵ|Â|Ấ|Ầ|Ẩ|Ẫ|Ậ',
            'D'=>'Đ',
            'E'=>'É|È|Ẻ|Ẽ|Ẹ|Ê|Ế|Ề|Ể|Ễ|Ệ',
            'I'=>'Í|Ì|Ỉ|Ĩ|Ị',
            'O'=>'Ó|Ò|Ỏ|Õ|Ọ|Ô|Ố|Ồ|Ổ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ở|Ỡ|Ợ',
            'U'=>'Ú|Ù|Ủ|Ũ|Ụ|Ư|Ứ|Ừ|Ử|Ữ|Ự',
            'Y'=>'Ý|Ỳ|Ỷ|Ỹ|Ỵ',
        );

        foreach($unicode as $nonUnicode=>$uni){
            $str = preg_replace("/($uni)/i", $nonUnicode, $str);
        }

        return $str;
    }

    /**
     * Get search string
     *
     * @param $str
     * @return string
     */
    public function getSearchString($str)
    {
        return strtolower($this->cleanSpecialCharacters($this->cleanVietnameseAccents($str)));
    }

    /**
     * Convert string to phone number format
     *
     * @param $str
     * @return string|null
     */
    public function phoneNumberFormat($str) {
        if (strlen($str) == 10) {
            if (preg_match('/(\d{3})(\d{3})(\d{4})$/', $str, $matches)) {
                return $matches[1] . ' ' . $matches[2] . ' ' . $matches[3];
            }
        } else if (strlen($str) == 11) {
            if (preg_match('/(\d{2})(\d{2})(\d{3})(\d{4})$/', $str, $matches)) {
                return '+' . $matches[1] . ' ' . $matches[2] . ' ' . $matches[3] . ' ' . $matches[4];
            }
        }
        return null;
    }

    /**
     * Check the file is a image by the file name extension
     */
    public function isImageFileByExtension($filePath) {
        $regex = '/[\w\-]+\.(png|jpg|gif|bmp|jpeg|PNG|JPG|GIF|BMP|JPEG)$/';
        return preg_match($regex, $filePath);
    }
    /**
     * Check the file is a image by the file name extension
     */
    public function isImageFileByExtensionAll($filePath) {
        $regex = '/[\w\-]+\.(png|jpg|gif|bmp|jpeg|PNG|JPG|GIF|BMP|JPEG|ico|tiff|webp|svg|apng|avif)$/';
        return preg_match($regex, $filePath);
    }
    /**
     * Add newline character p element
     */
    public function newLineCharacter($element)
    {
        $element .= "</p>".PHP_EOL;
        return $element;
    }
    /**
	* Escape html
    * @param string $str
    * @return string
	*/
	public static function escapeHtml($str) {
        $search = ['<', '>'];
        $replace = ['&lt;', '&gt'];
        return str_replace($search, $replace, $str);
	}

    /**
	* Escape html for summernote
    * @param string $str
    * @return string
	*/
	public static function escapeHtmlForSummernote($str) {
        $search = ['&lt;', '&gt;'];
        $replace = ['<span><</span>', '<span>></span>'];
        return str_replace($search, $replace, $str);
	}

    /**
     * Format input string where like
     */
    public function formatStringWhereLike($string){
        return addcslashes($string,'%_\\');
    }
     /**
	* Convert accented letters to unsigned , remove ",.\/\?;:|{}%#" characters and replace spaces with "_"
    * @param string $string
    * @param string $replacement
    * @return string
	*/
    public function create_slug($string,$replacement)
    {
        $search = array(
            '#(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)#',
            '#(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)#',
            '#(ì|í|ị|ỉ|ĩ)#',
            '#(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)#',
            '#(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)#',
            '#(ỳ|ý|ỵ|ỷ|ỹ)#',
            '#(đ)#',
            '#(À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ)#',
            '#(È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ)#',
            '#(Ì|Í|Ị|Ỉ|Ĩ)#',
            '#(Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ)#',
            '#(Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ)#',
            '#(Ỳ|Ý|Ỵ|Ỷ|Ỹ)#',
            '#(Đ)#',
            '/[^a-zA-Z0-9\-\_]*[,.\?\;\:\|\/{}%#]/' //ký tự khác với các từ:[a-z,A-Z,0-9]và những ký tự có từ: [,.\/\?;:|{}%#] sẽ thay thế bằng '-'  */        
        );
        $replace = array(
            'a',
            'e',
            'i',
            'o',
            'u',
            'y',
            'd',
            'A',
            'E',
            'I',
            'O',
            'U',
            'Y',
            'D',
            '',
        );
        $string = preg_replace($search, $replace, $string);
        $string = preg_replace('!\s+!', $replacement, $string); //loại bỏ trường hợp nhiều khoảng trắng và thay thế bằng 1 ký tự truyền vào
        $string = preg_replace('/( )/',$replacement, $string); //thay thế khoảng trắng thành ký tự truyền vào
        $string = strtolower($string);
        return $string;
    }

    /**
	* extract text in string with regex pattern
    * @param string $string
    * @param string $pattern, default pattern extract link 
    * @return string[]
	*/
    public static function extractTextWithPattern($string, $pattern = '/https?:\/\/\S+/i'){
        preg_match_all($pattern, $string, $matches);
        return $matches[0];
    }

    /**
     * Convert protocol to lowercase
     * @param $string
     * @return array|string|string[]|null
     */
    public function convertProtocolToLowercase($string) {
        $pattern = '/(https?):\/\//i';
        return preg_replace_callback($pattern, function($matches) {
            return strtolower($matches[0]);
        }, $string);
    }


    /**
     * Convert uppercase first character
     * @param $string
     * @return string
     */
    public function upperCaseFirst($string)
    {
        $langCode = auth()->check()
            ? optional(auth()->user()->language)->code
            : app()->getLocale();  
        return $langCode === Language::JA ? $string : ucfirst($string);
    }

    /**
     * Convert string search name
     * @param string $fullname
     * @return string
     */
    public function transformSearchFullname(string $fullname)
    {
        $chars = preg_split('//u', $fullname, -1, PREG_SPLIT_NO_EMPTY);
        foreach ($chars as $key => $char) {
            if ($char === 'Đ' || $char === 'đ') {
                $chars[$key] = 'd';
                continue;
            }
            if ($this->isLatinChar($char)) {
                $char = transliterator_transliterate('NFD; [:Nonspacing Mark:] Remove; NFC', $char);
                $chars[$key] = mb_strtolower($char, 'UTF-8');
            }
        }
        return join('', $chars);
    }

    /**
     * Check char is latin
     * @param string $char
     * @return bool
     */
    public function isLatinChar(string $char)
    {
        return preg_match('/^\p{Latin}$/u', $char) === 1;
    }
}
