<?php

namespace App\Http\Middleware;

use App\Models\Language;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class DateTimeFormat
{
    const DATETIME_FORMAT = [
        Language::VI => [
            'date' => 'd/m/Y',
            'regex_date' => '/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/([0-9]{4})$/',
            'month' => 'm/Y',
            'regex_month' => '/^(0[1-9]|1[0-2])\/([0-9]{4})$/',
            'datetime' => 'd/m/Y H:i',
            'regex_datetime' => '/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/([0-9]{4}) ([01]?[0-9]|2[0-3]):([0-5][0-9])$/',
        ],
        Language::EN => [
            'date' => 'Y-m-d',
            'regex_date' => '/^([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/',
            'month' => 'Y-m',
            'regex_month' => '/^([0-9]{4})-(0[1-9]|1[0-2])$/',
            'datetime' => 'Y-m-d H:i',
            'regex_datetime' => '/^([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]) ([01]?[0-9]|2[0-3]):([0-5][0-9])$/',
        ],
        Language::JA => [
            'date' => 'Y年m月d日',
            'regex_date' => '/^([0-9]{4})年(0[1-9]|1[0-2])月(0[1-9]|[1-2][0-9]|3[0-1])日$/',
            'month' => 'Y年m月',
            'regex_month' => '/^([0-9]{4})年(0[1-9]|1[0-2])月$/',
            'datetime' => 'Y年m月d日 H時i分',
            'regex_datetime' => '/^([0-9]{4})年(0[1-9]|1[0-2])月(0[1-9]|[1-2][0-9]|3[0-1])日 ([01]?[0-9]|2[0-3])時([0-5][0-9])分$/',
        ],
    ];

    const DATETIME_FIELDS = [
        'birthday',
        'from',
        'to',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $language = App::getLocale();

        if ($language == Language::VI) {
            return $next($request);
        }

        foreach (self::DATETIME_FIELDS as $field) {
            if (!$request->has($field)) {
                continue;
            }
            // Convert date format
            if (preg_match(self::DATETIME_FORMAT[$language]['regex_date'], $request->$field)) {
                $request->merge([
                    $field => Carbon::createFromFormat(self::DATETIME_FORMAT[$language]['date'], $request->$field)
                        ->format(self::DATETIME_FORMAT[Language::VI]['date']),
                ]);
            }
            // Convert month format
            if (preg_match(self::DATETIME_FORMAT[$language]['regex_month'], $request->$field)) {
                $request->merge([
                    $field => Carbon::createFromFormat(self::DATETIME_FORMAT[$language]['month'], $request->$field)
                        ->format(self::DATETIME_FORMAT[Language::VI]['month']),
                ]);
            }
            // Convert datetime format
            if (preg_match(self::DATETIME_FORMAT[$language]['regex_datetime'], $request->$field)) {
                $request->merge([
                    $field => Carbon::createFromFormat(self::DATETIME_FORMAT[$language]['datetime'], $request->$field)
                        ->format(self::DATETIME_FORMAT[Language::VI]['datetime']),
                ]);
            }
        }

        return $next($request);
    }
}
