.project-box {
    &:hover {
        box-shadow: 0 5px 10px rgba(#000, .13), 0 5px 10px rgba(#000, .2);
    }
    &__pin {
        color: #41b56c;
        span {
            @include rotate(45deg);
            font-size: 1.2em;
            border-radius: 50%;
            width: 12px;
            height: 15px;
            line-height: 1;
            cursor: pointer;
            &:before {
                color: #0791A3;
                z-index: 1;
                position: absolute;
                left: 0;
                top: 0;
                transition: all ease .3s;
            }
            &:after {
                content: '';
                width: 12px;
                height: 3px;
                background: $gray-300;
                border-radius: 50%;
                position: absolute;
                bottom: -5px;
                left: 50%;
                z-index: 0;
                @include translate(-50%, 0);
                transition: all ease .3s;
            }
        }
        input[type=checkbox]:checked+span {
            display: inline-block;
            font-weight: bold;
            background: none;
            &:before {}
            &:after {
                // top: -2px;
                transform: translate(-50%, 0) scale(1.25);
                background: $gray-400;
            }
        }
    }
}

.progress {
    background-color: #47dafc;
    height: 0.75rem;
    border-radius: 20px;
    .progress-bar {
        background-color: #2c77ca;
        border-radius: 20px;
    }
}

.progress-danger {
    .progress-bar {
        background-color: #b11a3b;
    }
}

.card {
    border-radius: 0;
    box-shadow: 0 0;
    border: 1px solid rgba(0, 0, 0, 0.13);
    .form-group {
        .form-control {
            border-radius: 0;
        }
        .input-group-append {
            background-color: #6A707E1A;
            .input-group-text {
                border-radius: 0;
            }
        }
        .select2-container--bootstrap4 {
            .select2-selection--single {
                height: calc(1.5em + .75rem + 4px) !important;
            }
        }
        .note-editor {
            overflow: visible;
            .note-toolbar {
                position: absolute;
                right: 0;
                top: -42px;
                border-bottom: 0;
                .note-view {
                    margin-right: 0;
                }
            }
        }
    }
    .select2-container--bootstrap4 {
        .select2-selection {
            border-radius: 0;
        }
    }
}

.theia-sidebar {
    padding-right: 0 !important;
    .btn {
        min-width: 158px;
        height: 50px;
        border-radius: 3px;
        opacity: 1;
        font: normal normal normal 16px/54px Noto Sans JP;
        letter-spacing: 0;
        color: #FFFFFF;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .card-body {
        .btn {
            min-width: 158px;
            height: 50px;
            border-radius: 3px;
            opacity: 1;
            font: normal normal normal 16px/54px Noto Sans JP;
            letter-spacing: 0px;
            color: #FFFFFF;
        }
    }
    .group-button {
        display: flex;
        flex-wrap: wrap;
        margin-left: -5px;
        margin-right: -5px;
        .btn {
            flex: 1;
            margin-left: 5px;
            margin-right: 5px;
            min-width: unset;
            &.back_reset_close {
                margin-top: 0;
                background-color: $white;
                color: #000000;
                border: 1px solid #DDDFE1;
                &:hover,
                &:focus,
                &:active {
                    color: black;
                    background-color: #FAFAFA;
                }
                a {
                    white-space: nowrap;
                }
            }
            @media (max-width: 640px) {
                max-width: 100%;
            }
        }
        &:not(.project) {
            .btn {
                max-width: 100%;
            }
        }
    }
}

.theia-sidebar .btn[type="reset"] {
    background-color: $white;
    color: #000000;
    border: 1px solid #DDDFE1;
    &:hover,
    &:focus,
    &:active {
        color: black;
        background-color: #FAFAFA;
    }
}

.content-wrapper {
    background-color: #fff;
}

.project-header {
    border-bottom: 0;
    span {
        font-size: 12px;
        color: #6c757d;
        .bg-green,
        .bg-red,
        .bg-gray {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 0;
            position: relative;
            top: 3px;
            margin: 0px 2px;
        }
    }
    .name-project {
        font-weight: bold;
        font-size: 17px;
        color: black;
    }
    .avatar-employees {
        width: 23px;
        height: 23px;
    }
    .fa-folder-plus {
        font-size: 19px;
    }
    .fa-clock {
        font-size: 19px;
    }
}

.project-body {
    padding-top: 0;
    padding-bottom: 16px;
    .row {
        min-height: 80px;
        box-shadow: 0.47px 3px 10px #7777771A;
        border-radius: 12px;
        .time-start,
        .time-end,
        .estimated-time,
        .real-time,
        .progress-project {
            text-align: center;
            // height: 30px;
            display: flex;
            div {
                margin: auto;
            }
        }
        .time-start {
            flex: 1;
            margin-right: 5px;
            background-color: #fff;
            .triangle {
                display: none;
            }
        }
        .time-end {
            flex: 1;
            margin-right: 5px;
            background-color: #fff;
            .triangle {
                display: none;
            }
        }
        .estimated-time {
            background-color: #fff;
            flex: 1;
            margin-right: 5px;
        }
        .real-time {
            flex: 1;
            background-color: #fff;
        }
        .progress-project {
            flex: 1;
            background-color: #fff;
        }
        .description-project {
            border: 1px solid #fff;
            border-top: 0;
            display: none;
        }
    }
}

.row {
    .card-header {
        border-radius: 0;
        background-color: #E9E9E9;
    }
    .card-body {
        .table-responsive {
            height: auto;
        }
        .open,
        .closed,
        .total {
            text-align: center;
        }
        .table th,
        td {
            border: 1px solid #dee2e6;
        }
        .table-document {
            td {
                border-right: 1px solid #dee2e6;
            }
        }
    }
}
.activity-history-notice{
    .activity-history__list{
        .activity-history__group{
            .activity-history__items{
                .activity-history__item{
                    .activity-history_priority{
                        width: 50px;
                        height: 24px;
                        span{
                           width: 100%;
                           height: 100%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                }
            }
        }
    }
}
@media screen and (max-width:1500px){
    .time-start div span{
        display: block;
    }
    .time-end div span {
        display: block;
    }
}

.work-time__date {
    display: flex;
    justify-content: space-between;
}

@media screen and (max-width:1700px) {
    .work-time__date {
        flex-direction: column;
    }
}

.short_information {
    margin: 0 0 50px 0;
    padding: 0;
    list-style: none;
    li {
        display: flex;
        justify-content: space-between;
        color: #464A53;
        padding: 10px 0px;
        border-bottom: 1px solid #ccc;
        .blue {
            color: #0791A3;
        }
    }
}

.card-project-profile {
    .form-image__view {
        width: 120px;
        height: 120px;
    }
}

.form-project__avatar {
    color: #464A53;
    font-size: 22px;
    margin-bottom: 30px;
}

.table-projects {
    table {
        border-collapse: separate;
        border-spacing: 0 10px;
    }
    table tbody {
        tr {
            transition: all linear .2s;
            background: #FFFFFF 0% 0% no-repeat padding-box;
            box-shadow: 0.47px 3px 10px #7777771A;
            border-radius: 12px;
            opacity: 1;
            &:hover {
                box-shadow: 3px 0.96px 30px #77777791;
                background: #FFFFFF 0% 0% no-repeat padding-box;
            }
        }
        tr td {
            position: relative;
        }
        td:last-child {
            border-radius: 0 12px 12px 0;
            position: relative;
        }
        td:first-child {
            border-radius: 12px 0 0 12px;
        }
    }
    .table th,
    .table td {
        border: 0;
    }
    .table th {
        color: #6A707E;
        font-size: 14px;
        font-weight: normal;
        a {
            color: #6A707E;
            .fas {
                font-weight: normal;
                margin-left: 5px;
            }
            .fa-sort {
                &:before {
                    content: "\f107";
                }
            }
            .fa-sort-up {
                &:before {
                    content: "\f106";
                }
            }
        }
    }
    .progress {
        height: 22px;
        background: #EFF0F0 0% 0% no-repeat padding-box;
        border-radius: 53px;
        .progress-number {
            font-weight: normal;
            font-size: 16px;
            line-height: inherit;
            // margin-top: -2px;
        }
        .progress-bar {
            &.progress-danger {
                background: #FF0000 0% 0% no-repeat padding-box;
            }
            &.progress-info {
                background: #409834 0% 0% no-repeat padding-box;
            }
            &.progress-success {
                background: #0791A3 0% 0% no-repeat padding-box;
            }
        }
    }
}

label.work_time {
    width: 110px;
    height: 33px;
    background: #0791A3 0% 0% no-repeat padding-box;
    border-radius: 60px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #FFFFFF;
    opacity: 1;
    margin-bottom: 0;
}

span.work_date {
    font-size: 16px;
    color: #464A53;
    margin-right: 5px;
}

#clock {
    font-size: 23px;
    color: #464A53;
}

ul.static_count {
    margin: 0;
    padding: 0;
    list-style: none;
    li {
        background: #FAFAFA 0% 0% no-repeat padding-box;
        border-radius: 60px;
        opacity: 1;
        letter-spacing: 0px;
        color: #464A53;
        padding: 12px 30px;
        display: flex;
        justify-content: space-between;
        &:not(:last-child) {
            margin-bottom: 13px;
        }
        .count {
            background: #FF0000 0% 0% no-repeat padding-box;
            border-radius: 60px;
            opacity: 1;
            width: 30px;
            height: 20px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
    }
}

.time-of__day {
    color: #464A53;
    font-size: 14px;
}

.project-statistical {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .project-title__statistical {
        color: #464A53;
        font-size: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
        span {
            color: #FFFFFF;
            font-size: 14px;
            background: #7C7C7C 0% 0% no-repeat padding-box;
            border-radius: 60px;
            width: 39px;
            height: 27px;
            line-height: 27px;
            text-align: center;
        }
    }
    .btn-add-project {
        width: 158px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
            width: 18px;
            height: 18px;
            margin-right: 10px;
        }
    }
}

.card-notice__item {
    display: flex;
    justify-content: space-between;
    padding: 12px 10px;
    &:not(:last-child) {
        border-bottom: 1px solid #ccc;
    }
    // &:nth-of-type(odd) {
    //     background: #FAFAFA 0% 0% no-repeat padding-box;
    // }
    &:hover {
        background: #EFEFEF;
    }
    .card-notice__avatar {
        flex: 0 0 46px;
        max-width: 46px;
        margin-right: 10px;
        img {
            border: 1px solid #CCCCCC;
            width: 46px;
            height: 46px;
            border-radius: 50%;
            display: block;
        }
    }
    .notice-info__wrap {
        display: flex;
        justify-content: space-between;
        color: #4D4D4D;
        font-size: 14px;
        .notice-info__left {
            p {
                margin-bottom: 0;
            }
        }
        .notice-info__right {
            font-size: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            .btn {
                background: #FF0000 0% 0% no-repeat padding-box;
                border-radius: 40px;
                color: #fff;
                min-width: 70px;
            }
        }
    }
}

.activity-history__date {
    background: #FAFAFA 0% 0% no-repeat padding-box;
    color: #4D4D4D;
    font-size: 14px;
    padding: 14px 18px;
}

.activity-history__item {
    border-bottom: 1px solid #ccc;
    display: flex;
    gap: 20px;
    padding: 15px 20px;
}

.activity-history__time {
    font-size: 12px;
}

.activity-history__content {
    color: #4D4D4D;
    h4 {
        margin-bottom: 7px;
    }
    a {
        color: #0791A3;
        font-size: 16px;
    }
    p {
        font: normal normal normal 14px/21px Noto Sans JP;
        margin-bottom: 0;
    }
}

.activity-history__item {
    flex: 1;
}

.activity-history__content {
    flex: 8;
}

.activity-history__action {
    flex: 2;
    text-align: right;
    span {
        font-size: 14px;
        color: #FFFFFF;
        background: #0791A3 0% 0% no-repeat padding-box;
        padding: 8px 15px;
    }
}

.project-text {
    color: #0791A3 !important;
	font-size: 18px;
}

.table-task-project {
    font-size: 14px;
    font-weight: normal;
}

.card-title__group {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

h4.card-title__sub {
    margin-bottom: 0;
}

.staff-overtime_working {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-bottom: 1px solid #ccc;
    padding: 12px 0px;
    min-width: 400px;
}

.overtime-working_no {
    flex: 0 0 25px;
    max-width: 25px;
    color: #7C7C7C;
}

.overtime-working_staff {
    flex: 0 0 200px;
    max-width: 200px;
    a {
        display: flex;
        align-items: center;
        gap: 13px;
    }
}

.overtime-working__time {
    margin-left: auto;
    min-width: 90px;
    padding-left: 5px;
    &.danger span {
        color: #FF0000;
    }
    &.warning span {
        color: #FF9301;
    }
    &.success span {
        color: #51BE42;
    }
    &.default span {
        color: #088493;
    }
}

.overtime-working__progress {
    margin-left: auto;
    width: 100%;
    max-width: 100px;
    .progress {
        width: 100px;
        border-radius: 50px;
        &.danger {
            background: #FF0000 0% 0% no-repeat padding-box;
        }
        &.warning {
            background: #FF9301 0% 0% no-repeat padding-box;
        }
        &.success {
            background: #51BE42 0% 0% no-repeat padding-box;
        }
        &.default {
            background: #088493 0% 0% no-repeat padding-box;
        }
    }
}

.project-management__overtime {
    h4.card-title__sub {
        font-size: 22px;
        color: #464A53;
        margin-bottom: 2px;
    }
    .card-title__desc {
        font-size: 14px;
        color: #AAAAAA;
    }
}

.chart-doughnut {
    align-items: center;
}

.chartjs-compare {
    p {
        margin-bottom: 0;
    }
    p:first-child {
        font-size: 32px;
        color: #464A53;
    }
    p:last-child {
        font-size: 16px;
        color: #FF0000;
    }
}

.chartjs-note {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    span {
        position: relative;
        &::after {
            content: '';
            display: block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: -8px;
            transform: translate(-50%, -50%);
        }
    }
    .red {
        color: #FF0000;
        &::after {
            background: red;
        }
    }
    .orange {
        color: #FF9301;
        &::after {
            background: #FF9301;
        }
    }
    .green {
        color: #51BE42;
        &::after {
            background: #51BE42;
        }
    }
    .blue {
        color: #088493;
        &::after {
            background: #088493;
        }
    }
}

.project-management__statistic {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 10px #7777771A;
    border-radius: 12px;
    padding: 32px 35px;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: start;
    font-size: 17px;
    color: #464A53;
    margin-top: 27px;
    gap: 80px;
    .calendar-icon {
        margin-right: 30px;
    }
    .next-time {
        padding: 0 30px;
    }
    .font-22 {
        font-size: 22px;
    }
    .font-18 {
        font-size: 18px;
    }
    .empty {
        display: none;
    }
    .item-1 {
        .item-5 {
            display: none;
        }
        .item-3 {
            display: none;
        }
    }
    .item-2 {
        .item-6 {
            display: none;
        }
        .item-4 {
            display: none;
        }
    }
    .item-3 {
        .item-4 {
            display: none;
        }
        .item-5 {
            display: none;
        }
    }
    .item-4 {
        .item-6 {
            display: none;
        }
    }
}

.project-management__statistic_vi,
.project-management__statistic_en {
    .work-static-member-custom {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        gap: 18px 0;
    }
    .item-5,
    .item-6 {
        display: none;
    }
    .item-1 {
        .empty {
            display: flex;
            gap: 13px;
        }
    }
    .item-2 {
        .empty {
            display: flex;
            gap: 13px;
        }
    }
    .item-3 {
        .item-5 {
            display: flex;
            gap: 13px;
        }
    }
    .item-4 {
        .item-6 {
            display: flex;
            gap: 13px;
        }
    }
}

.work-static-member {
    display: flex;
    gap: 13px;
    .blue {
        color: #088493;
    }
}

.total-overtime-working-hours {
    margin-bottom: 50px;
    overflow-x: auto;
}

@media(max-width: 1790px) {
    .project-management__statistic {
        padding: 32px 46px 29px 35px;
        justify-content: space-between;
        .work-static-member-custom {
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            gap: 18px 0;
        }
    }
    .project-management__statistic_ja {
        .item-5,
        .item-6 {
            display: none;
        }
        .item-1 {
            .empty {
                display: flex;
                gap: 13px;
            }
        }
        .item-2 {
            .empty {
                display: flex;
                gap: 13px;
            }
        }
        .item-3 {
            .item-5 {
                display: flex;
                gap: 13px;
            }
        }
        .item-4 {
            .item-6 {
                display: flex;
                gap: 13px;
            }
        }
    }
    .project-management__statistic_vi,
    .project-management__statistic_en {
        .item-1 {
            .empty {
                display: none;
            }
            .item-5 {
                display: flex;
                gap: 13px;
            }
        }
        .item-2 {
            .empty {
                display: none;
            }
            .item-6 {
                display: flex;
                gap: 13px;
                margin-left: 39px;
            }
        }
        .item-3 {
            .item-5 {
                display: none;
            }
            .item-4 {
                display: flex;
                gap: 13px;
            }
        }
        .item-4 {
            display: none;
        }
    }
}

@media(max-width: 1600px) {
    .chartjs-compare p:first-child {
        font-size: 24px;
    }
    .chartjs-compare p:last-child {
        font-size: 14px;
    }
    .chartjs-note {
        font-size: 12px;
    }
}

@media (min-width: 992px) and (max-width: 1598px) {
    .short_information {
        li:nth-child(1),
        li:nth-child(2) {
            flex-direction: column;
        }
    }
}

@media(max-width: 1400px) {
    .project-management__statistic {
        padding: 12px 18px;
    }
}

@media(max-width: 1467px) {
    .project-management__statistic_vi,
    .project-management__statistic_en {
        .item-1 {
            .item-3 {
                display: flex;
                gap: 13px;
            }
        }
        .item-2 {
            .item-4 {
                display: flex;
                gap: 13px;
                margin-left: 37px;
            }
        }
        .item-3 {
            display: none;
        }
    }
}

@media(max-width: 1378px) {
    .project-management__statistic {
        padding: 32px 60px 29px 35px;
    }
    .project-management__statistic_ja {
        .item-1 {
            .empty {
                display: none;
            }
            .item-5 {
                display: flex;
                gap: 13px;
            }
        }
        .item-2 {
            .empty {
                display: none;
            }
            .item-6 {
                display: flex;
                gap: 13px;
                margin-left: 39px;
            }
        }
        .item-3 {
            .item-5 {
                display: none;
            }
            .item-4 {
                display: flex;
                gap: 13px;
            }
        }
        .item-4 {
            display: none;
        }
    }
}

@media(max-width: 1196px) {
    .project-management__statistic_vi,
    .project-management__statistic_en {
        flex-direction: column;
        gap: 18px 0;
        .empty {
            display: none;
        }
        .item-1 {
            .item-5 {
                display: none;
            }
            .item-3 {
                display: none;
            }
        }
        .item-2 {
            .item-6 {
                display: none;
            }
            .item-4 {
                display: none;
            }
        }
        .item-3 {
            display: block;
            .item-4 {
                display: none;
            }
            .item-5 {
                display: none;
            }
        }
        .item-4 {
            display: block;
            .item-6 {
                display: none;
            }
        }
        .item-5,
        .item-6 {
            display: flex;
            gap: 13px;
        }
    }

    .list-project-screen{
        .row{
            .col-lg-9, .col-lg-3{
                max-width: 100% !important;
                flex: 0 0 100%;
            }
        }
    }
}

@media(max-width: 1171px) {
    .project-management__statistic_ja {
        .item-1 {
            .item-3 {
                display: flex;
                gap: 13px;
            }
        }
        .item-2 {
            .item-4 {
                display: flex;
                gap: 13px;
                margin-left: 37px;
            }
        }
        .item-3 {
            display: none;
        }
    }
}

@media(max-width: 992px) {
    .project-management__statistic_ja {
        .item-1 {
            .item-3 {
                display: none;
            }
        }
        .item-2 {
            .item-4 {
                display: none;
            }
        }
        .item-3 {
            display: flex;
        }
    }
    .project-management__statistic_vi,
    .project-management__statistic_en {
        flex-direction: unset;
        gap: 18px 0;
        .empty {
            display: none;
        }
        .item-1 {
            .item-5 {
                display: block;
            }
            .item-3 {
                display: block;
            }
        }
        .item-2 {
            .item-6 {
                display: block;
            }
            .item-4 {
                display: block;
            }
        }
        .item-3,
        .item-4,
        .item-5,
        .item-6 {
            display: none;
        }
    }
}

@media(max-width: 832px) {
    .project-management__statistic_ja {
        .item-1 {
            .item-3 {
                display: block;
            }
        }
        .item-2 {
            .item-4 {
                display: block;
            }
        }
        .item-3 {
            display: none;
        }
    }
}

@media(max-width: 775px) {
    .project-management__statistic_vi,
    .project-management__statistic_en {
        flex-direction: column;
        gap: 18px 0;
        padding: 32px 10px;
        .empty {
            display: none;
        }
        .item-1 {
            .item-5 {
                display: none;
            }
            .item-3 {
                display: none;
            }
        }
        .item-2 {
            .item-6 {
                display: none;
            }
            .item-4 {
                display: none;
            }
        }
        .item-3 {
            display: block;
            .item-4 {
                display: none;
            }
            .item-5 {
                display: none;
            }
        }
        .item-4 {
            display: block;
            .item-6 {
                display: none;
            }
        }
        .item-5,
        .item-6 {
            display: block;
        }
    }
}

@media(min-width: 1200px) {
    .project-management__overtime {
        .card .card-body {
            padding: 30px;
        }
    }
}

@media (max-width: 768px) {
    .chart-doughnut .col-md-7 {
        max-width: 400px;
        margin: auto;
    }
}

@media(max-width: 599px) {
    .project-management__statistic_vi,
    .project-management__statistic_en {
        .work-static-member {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}

@media(max-width: 500px) {
    .activity-history__item {
        flex-direction: column;
        justify-content: left;
        align-items: baseline;
        padding-left: 0;
        padding-right: 0;
        gap: 12px;
    }
}

@media (max-width: 640px) {
    .project-statistical {
        flex-direction: column;
    }
    .card-notice__list {
        .card-notice__item {
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 12px;
            .card-notice__avatar {
                margin-right: 0;
            }
            .card-notice__info .notice-info__wrap {
                display: flex;
                flex-direction: column;
                justify-content: center;
                text-align: center;
                align-items: center;
                gap: 10px;
            }
        }
    }
    .theia-sidebar #butonAddMember.btn {
        max-width: 100%;
        margin-top: 10px;
    }
}

.action-add-member .btn-editemploy {
    min-width: unset !important;
}

@media (max-width: 605px) {
    .project-management__statistic {
        flex-direction: column;
        .calendar-icon {
            margin-right: 15px;
        }
        .next-time {
            padding: 0 15px;
        }
    }
    .project-management__statistic_ja {
        gap: 18px 0;
        .empty {
            display: none;
        }
        .item-1 {
            .item-5 {
                display: none;
            }
            .item-3 {
                display: none;
            }
        }
        .item-2 {
            .item-6 {
                display: none;
            }
            .item-4 {
                display: none;
            }
        }
        .item-3 {
            display: block;
            .item-4 {
                display: none;
            }
            .item-5 {
                display: none;
            }
        }
        .item-4 {
            display: block;
            .item-6 {
                display: none;
            }
        }
        .item-5,
        .item-6 {
            display: block;
        }
    }
}

.badge-primary.badge-filter {
    background-color: #7C7C7C;
}

.card-calendar {
    padding: 0 15px 15px 15px;
}

.fc-theme-standard th.c-day-sun {
    background: #fff;
}

.card-timekeeping {
    padding: 15px;
    .fc-col-header-cell {
        height: 30px;
    }
    .fc-theme-standard td,
    .fc-theme-standard th {
        border: 1px solid #DDDFE1;
    }
    .fc-theme-standard th {
        border-bottom: 0;
    }
    .fc-theme-standard .fc-scrollgrid {
        border: 0;
    }
    .fc .fc-col-header-cell-cushion {
        color: #000000;
        font-size: 16px;
        font-weight: normal;
    }
    h3.date-choose {
        font-weight: normal;
        font-size: 22px;
        color: #464A53;
    }
    h3.timekeeping-static-header {
        color: #464A53;
        font-size: 22px;
        font-weight: normal;
    }
    .fc-daygrid-day-number {
        color: #000000;
        font-size: 22px;
    }
    .fc .fc-day-sat,
    .fc .fc-day-sun {
        background: #fff!important;
    }
    .fc-day-other {
        background-color: #fff !important;
        .fc-daygrid-day-number {
            color: #DDDFE1;
        }
    }
    .fc-daygrid-block-event .fc-event-time {
        color: #4E4E4E;
        font-size: 14px;
        font-weight: normal;
    }
    .fc-daygrid-day-bg {
        font-size: 14px;
    }
    .fc-scrollgrid-sync-table {
        tr:first-child td {
            border-top: 0;
        }
    }
}