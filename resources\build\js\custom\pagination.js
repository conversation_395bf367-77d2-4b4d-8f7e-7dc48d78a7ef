var checkbox = [];
$(document).on('click', "#select-all",function(event){
    $('.checkbox-select').prop('checked', this.checked);
    if($(this).is(':checked')){
        $('.checkbox-select').each(function() {
            if(!checkbox.includes($(this).data('select'))){
                checkbox.push( $(this).data('select'));
            }
        });
    }
    else{
        $('.checkbox-select').each(function() {
            checkbox.pop( $(this).data('select'));
        });
    }
})
$(document).on('click', ".checkbox-select",function(event) {
    if($(this).is(':checked')){
        checkbox.push( $(this).data('select'));
    }
    else{
        if($("#select-all").is(':checked')){
            $('#select-all').prop('checked', false);
        }
        checkbox = checkbox.filter(item => item !== $(this).data('select'))
    }
})
$(document).on('click', ".panigation-ajax .pagination .page-link",function(event){
    event.preventDefault();
    let url = $(this).attr('href')
    $.ajax({
        url: url,
        type: "GET",
        success: function(result){
            if (result.status === 200){
                $('.table-list-data').html(result.html);
                $('.tooltip.fade.bs-tooltip-top.show').remove();
                $('[data-toggle="tooltip"]').tooltip({
                    animation: true,
                    delay: 0
                  }); 
                let checkAll = true;
                $('.checkbox-select').each(function() {
                    if( checkbox.includes($(this).data('select'))){
                        $(this).prop('checked', true);
                    } else checkAll = false;
                 });
                if(checkAll === true){
                    $('#select-all').prop('checked', true);
                }
            } 
        },
        error:function() {
            
        }
    });
})
$(document).on('click', "#export",function(event) {
    var $this = $(this);       
    var _href = $this.attr("href"); 
    $this.attr("href", _href + '&select=' + checkbox);
})