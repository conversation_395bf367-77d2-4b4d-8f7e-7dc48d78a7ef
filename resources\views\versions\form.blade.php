@extends('layouts.master')
@section('title', trans('language.version_management'))

@section('css_library')
    @include('partials.style-library', ['select2' => true, 'icheck' => true])
@endsection

@section('js_library')
    <script src="{{ asset('plugins/jquery-validation/additional-methods.min.js') }}"></script>
    @include('partials.script-library', ['select2' => true, 'stickysidebar' => true])
@endsection

@section('header')
    <li class="nav-item">
        {{ trans('language.version_management') }}
    </li>
@endsection

@section('content')
    <section class="content pt-3">
        @include('partials.breadcrumb', [
           'item' => '<a href="' . route('versions.index') . '">' . trans('language.version_management') . '</a>
           &nbsp;/&nbsp;' . $headerTitle
       ])
        <form id="form-version" action="{{ $route }}" method="POST">
            @csrf
            @if ($isEdit)
                @method('PUT')
            @endif
            <div class="row ml-0 mx-0">
                <!-- Main Content Area -->
                <div class="col-xl-10 theia-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>{{ $headerTitle }}</h3>
                        </div>
                        <div class="card-body">
                            <!-- All form fields here -->
                            <!-- Version Name -->
                            <div class="form-group">
                                <label>{{ __('language.version_name') }} <span class="text-danger">*</span></label>
                                <input type="text" name="version" class="form-control"
                                    placeholder="{{ __('language.version_placeholder') }}"
                                    value="{{ old('version', $version->version ?? '') }}">
                                @if ($errors->first('version'))
                                    <div class="invalid-alert text-danger">{{ $errors->first('version') }}</div>
                                @endif
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Platforms Selection -->
                                    @if (!$isEdit)
                                        <div class="form-group">
                                            <label>{{ __('language.version_platform') }} <span
                                                    class="text-danger">*</span></label>
                                            <select name="platforms" class="select2-base" style="width: 100%">
                                                <option value="{{ \App\Models\AppVersion::ANDROID_AND_IOS }}"
                                                    {{ (int) old('platforms', $version->platforms ?? \App\Models\AppVersion::ANDROID_AND_IOS)
                                                                === \App\Models\AppVersion::ANDROID_AND_IOS ? 'selected' : '' }}>
                                                    {{ __('language.android_and_ios') }}
                                                </option>
                                                <option value="{{ \App\Models\AppVersion::IOS }}"
                                                    {{ (int) old('platforms', $version->platforms ?? '') === \App\Models\AppVersion::IOS ? 'selected' : '' }}>
                                                    {{ __('language.ios') }}
                                                </option>
                                                <option value="{{ \App\Models\AppVersion::ANDROID }}"
                                                    {{ (int) old('platforms', $version->platforms ?? '') === \App\Models\AppVersion::ANDROID ? 'selected' : '' }}>
                                                    {{ __('language.android') }}
                                                </option>
                                            </select>
                                            @if ($errors->first('platforms'))
                                                <div class="invalid-alert text-danger">{{ $errors->first('platforms') }}
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        <div class="form-group">
                                            <label>{{ __('language.version_platform') }} <span
                                                    class="text-danger">*</span></label>
                                            <select name="platforms" class="select2-base" style="width: 100%">
                                                <option value="{{ \App\Models\AppVersion::IOS }}"
                                                    {{ (int) old('platforms', $version->platform ?? '') === \App\Models\AppVersion::IOS ? 'selected' : '' }}>
                                                    {{ __('language.ios') }}
                                                </option>
                                                <option value="{{ \App\Models\AppVersion::ANDROID }}"
                                                    {{ (int) old('platforms', $version->platform ?? '') === \App\Models\AppVersion::ANDROID ? 'selected' : '' }}>
                                                    {{ __('language.android') }}
                                                </option>
                                            </select>
                                            @if ($errors->first('platforms'))
                                                <div class="invalid-alert text-danger">{{ $errors->first('platforms') }}
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <!-- Status Selection -->
                                    <div class="form-group">
                                        <label>{{ __('language.status') }} <span class="text-danger">*</span></label>
                                        <select name="status" class="select2-base" style="width: 100%">
                                            <option value="{{ \App\Models\AppVersion::DRAFT }}"
                                                {{ old('status', $version->status ?? \App\Models\AppVersion::DRAFT) == \App\Models\AppVersion::DRAFT ? 'selected' : '' }}>
                                                {{ __('language.draft') }}
                                            </option>
                                            <option value="{{ \App\Models\AppVersion::PUBLISHED }}"
                                                {{ old('status', $version->status ?? '') == \App\Models\AppVersion::PUBLISHED ? 'selected' : '' }}>
                                                {{ __('language.published') }}
                                            </option>
                                        </select>
                                        @if ($errors->first('status'))
                                            <div class="invalid-alert text-danger">{{ $errors->first('status') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Description with Default Content -->
                            <div class="form-group">
                                <label>{{ __('language.version_description') }}</label>
                                <textarea name="description" rows="5" class="form-control"
                                    placeholder="{{ __('language.version_description_placeholder') }}">{{ old('description', $defaultDescription) }}</textarea>
                                @if ($errors->first('description'))
                                    <div class="invalid-alert text-danger">{{ $errors->first('description') }}</div>
                                @endif
                            </div>

                            <!-- Release Notes with Default Content -->
                            <div class="form-group">
                                <label>{{ __('language.version_release_notes') }}</label>
                                <textarea name="release_notes" rows="7" class="form-control"
                                    placeholder="{{ __('language.version_release_notes_placeholder') }}">{{ old('release_notes', $defaultReleaseNotes) }}</textarea>
                                @if ($errors->first('release_notes'))
                                    <div class="invalid-alert text-danger">{{ $errors->first('release_notes') }}</div>
                                @endif
                            </div>

                            <!-- Force Update Checkbox -->
                            <div class=" mb-0 icheck-bee">
                                <input type="checkbox" name="force_update" value="1" id="isForceUpdate"
                                    {{ old('force_update', $version->force_update ?? false) ? 'checked' : '' }}>
                                <label for="isForceUpdate"
                                    class="form-check-label">{{ __('language.force_update') }}</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Single Floating Save Button -->
                <div class="col-xl-2 theia-sidebar">
                    <div class="group-button wrap-button__profile d-flex justify-content-center">
                        <button type="submit" class="btn btn-primary">
                            {{ trans('language.save') }}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </section>
@endsection
