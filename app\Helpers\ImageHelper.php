<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;

class ImageHelper
{
    /**
     * Resize the image and constrain aspect ratio
     *
     * @param $imagePath
     * @param $width
     * @param $height
     * @return \Intervention\Image\Image
     */
    public function resizeImage($inputPath, $width, $height, $outputPath = null)
    {
        if (!file_exists($inputPath)) {
            return false;
        }

        // Read the image from the input path
        $img = ImageManager::gd()->read($inputPath);


        // Resize the image while maintaining aspect ratio
        $img->resize($width, $height, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        // Get the format of the image
        $format = strtolower(pathinfo($inputPath, PATHINFO_EXTENSION));

        // Encode the image based on the format with high quality
        if ($format === 'png') {
            $img->encodeByExtension('png', 0);
        } else {
            $img->encodeByExtension(null, 100);
        }

        if ($outputPath) {
            // Make directory if not exist
            $directory = dirname($outputPath);
            if (!Storage::exists($directory)) {
                Storage::makeDirectory($directory, 0755, true);
            }

            // Save the image to the specified output path
            $img->save($outputPath);
        }

        return $img;
    }
}
