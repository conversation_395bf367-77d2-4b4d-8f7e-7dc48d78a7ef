//
// Component: Alert
//

.alert {
  .icon {
    margin-right: 10px;
  }

  .close {
    color: $black;
    opacity: .2;

    &:hover {
      opacity: .5;
    }
  }

  a {
    color: $white;
    text-decoration: underline;
  }
}

.invalid-alert{
  font-size: 0.8rem;
  margin-top: 5px;
  font-style: italic;
  &:before{
    font-family: $font-awesome;
    display: inline-block;
    font-style: normal;
    margin-right: 3px;
    font-weight: bold;
  }
  &.text-danger{
    &:before{
      content: '\f00d';
    }
  }
  &.text-warning{
    &:before{
      content: '\f12a';
    }
  }
  &.text-success{
    &:before{
      content: '\f00c';
    }
  }
}

//<PERSON><PERSON> Variants
@each $color, $value in $theme-colors {
  .alert-#{$color} {
    color: color-yiq($value);
    background-color: $value;
    border-color: darken($value, 5%);
  }

  .alert-default-#{$color} {
    @include alert-variant(theme-color-level($color, $alert-bg-level), theme-color-level($color, $alert-border-level), theme-color-level($color, $alert-color-level));
  }
}
