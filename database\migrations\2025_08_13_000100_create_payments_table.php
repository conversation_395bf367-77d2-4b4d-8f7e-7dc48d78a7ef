<?php

use App\Enums\PaymentStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('payments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('order_id');
            $table->string('channel'); // PaymentChannel
            $table->string('status')->default(PaymentStatus::INIT->value);
            $table->unsignedBigInteger('amount'); // duplicated from order at creation time
            $table->string('currency', 3)->default('VND');
            $table->unsignedInteger('attempt_no')->default(1); // retry support
            $table->string('provider_transaction_id')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->json('meta')->nullable(); // per-channel info
            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('orders')->cascadeOnDelete();
            $table->index(['order_id', 'status']);
            $table->index(['channel']);
        });
    }

    public function down(): void {
        Schema::dropIfExists('payments');
    }
};
