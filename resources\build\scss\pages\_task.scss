.task-history-item{
  padding: 10px 0;
  &:not(:last-child){
    border-bottom: 1px dashed #ddd;
  }
  a{
    display: inline-block;
  }

  &__header{
    h6{
      font-size: 14px;
      margin: 0 0 5px;
      display: inline-block;
    }
    .actions{
      font-size: 13px;
      font-weight: normal;
      float: right;
      white-space: nowrap;
      margin-bottom: 5px;
      a{
        margin-left: 10px;
        opacity: 0.5;
        &:hover, &:focus{
          opacity: 1;
        }
      }
    }
  }

  &__note{
    p,ul,ol,li{
      &:last-child{
        margin-bottom: 0;
      }
    }
  }

  &__list{
    color: #868686;
    padding-left: 20px;
    font-size: 12px;
    &:last-child{
      margin-bottom: 0;
    }
  }
  &__form-edit{
    display: none;
  }

  .avatar-employees{
    width: 24px;
    height: 24px;
  }
}

.nav-tabs.task-history-tab{
  border-bottom: 0;
}
.tab-content.task-history-content{
  border: 1px solid #ddd;
  border-radius: 0 5px 5px 5px;
}

.table-list-data {
  .task-start{
    background-color: #ccffd8 !important;
  }
  .warning
  {
    background-color: #fff3c8e0 !important;
  }
  .slow {
    background-color: #FFEFEF !important;
    // border: 1px solid #F2ACB0;
    td {
      border-left: 0px;
      border-top: 1px solid #F2ACB0;
      border-bottom: 1px solid #F2ACB0;
    }
    td:last-child {
      border-right: 1px solid #F2ACB0;
    }
    td:first-child {
      border-left: 1px solid #F2ACB0;
    }
  }
  .not-slow {
    td {
      border-bottom: 0px;
    }
  }
  td {
    .badge {
      padding: 5px 10px;
      border-radius: 15px;
      display: initial;
    }
    .bg-teal {
      color: black !important;
    }
  }
  .slow:hover{
    background-color: #fffbf0  !important;
  }
  .add-task-child {
    display: flex;
    float: left;
    .action-create{
      white-space: unset;
      i {
        position: unset;
        transform:unset;
      }
    }
  }
}