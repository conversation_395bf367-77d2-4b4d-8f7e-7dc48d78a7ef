export function formatDate(date, type){
    if (!date) {
        return null
    }
    let dateString;
    switch (type){
        case 'dd/mm/yyyy':
        case 'DD/MM/YYYY':
        case 'dd/MM/YYYY':
        case 'dd/mm/YYYY':
        case 'dd/MM/yyyy':
        case 'DD/MM/yyyy':
            dateString =
                ("0" + date.getDate()).slice(-2) + "/" +
                ("0" + (date.getMonth() + 1)).slice(-2) + "/" +
                date.getFullYear();
            break;
        case 'dd/mm/yyyy H:i':
            dateString =
                ("0" + date.getDate()).slice(-2) + "/" +
                ("0" + (date.getMonth() + 1)).slice(-2) + "/" +
                date.getFullYear() + " " +
                ("0" + date.getHours()).slice(-2) + ":" + ("0" + date.getMinutes()).slice(-2);
            break;
        case 'yyyy-mm-dd H:i':
            dateString =
                date.getFullYear() + "-" +
                ("0" + (date.getMonth() + 1)).slice(-2) + "-" +
                ("0" + date.getDate()).slice(-2) + " " +
                ("0" + date.getHours()).slice(-2) + ":" + ("0" + date.getMinutes()).slice(-2);
            break;
        case 'yyyy年mm月dd日 H時i分':
            dateString =
                date.getFullYear() + "年" +
                ("0" + (date.getMonth() + 1)).slice(-2) + "月" +
                ("0" + date.getDate()).slice(-2) + "日 " +
                ("0" + date.getHours()).slice(-2) + "時" + ("0" + date.getMinutes()).slice(-2) + "分";
            break;
        case 'yyyy-mm-dd':
            dateString =
                date.getFullYear()  + "-" + ("0" + (date.getMonth() + 1)).slice(-2) + "-" + ("0" + date.getDate()).slice(-2);
            break;
        case 'yyyy年mm月dd日':
            dateString =
                date.getFullYear()  + "年" + ("0" + (date.getMonth() + 1)).slice(-2) + "月" + ("0" + date.getDate()).slice(-2) + "日";
            break;
        case 'H:i':
            dateString = ("0" + date.getHours()).slice(-2) + ":" + ("0" + date.getMinutes()).slice(-2);
            break;
        case 'H時i分':
            dateString = ("0" + date.getHours()).slice(-2) + "時" + ("0" + date.getMinutes()).slice(-2) + "分";
            break;
        default:
    }
    
    return dateString
}