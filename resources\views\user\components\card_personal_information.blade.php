<div class="card card-profile" id="personal_information">
    <div class="card-header">
        {{ trans('language.user_personal_information') }}
    </div>
    <div class="card-body">
        <div class="col-wrap">
            <label for="">{{ trans('language.address') }}</label>
            <div class="row">
                @php
                    $choosePrefecture = old('prefecture_id') ?? ($user->province_code ?? '');
                    $chooseCommune = old('commune_id') ?? ($user->ward_code ?? '');
                @endphp
                <div class="col-sm-3">
                    <div class="form-group">
                        <select
                            class="select2-base dynamic-select-option 
                            {{ $errors->first('prefecture_id') ? 'is-invalid' : '' }}"
                            style="width: 100%" data-child="#select_ward" data-url="{{ route('getCommuneList') }}"
                            data-placeholder="{{ trans('language.choose_a_province') }}" name="prefecture_id"
                            {{ !$canEdit ? 'disabled' : '' }}>
                            <option value="" disabled selected style="display: none">
                                {{ trans('language.choose_prefecture') }}</option>
                            @foreach ($prefectures as $prefecture)
                                <option value="{{ $prefecture->province_code }}"
                                    {{ $choosePrefecture == $prefecture->province_code ? 'selected' : '' }}>
                                    {{ $prefecture->name }}
                                </option>
                            @endforeach
                        </select>
                        @if ($errors->first('prefecture_id'))
                            <div class="invalid-alert text-danger">{{ $errors->first('prefecture_id') }}</div>
                        @endif
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <select class="select2-base {{ $errors->first('commune_id') ? 'is-invalid' : '' }}"
                            id="select_ward" data-placeholder="{{ trans('language.choose_a_commune') }}"
                            name="commune_id" style="width: 100%" {{ !$canEdit ? 'disabled' : '' }}>
                            <option value="" disabled selected>{{ trans('language.choose_commune') }}</option>
                            @foreach ($communes as $commune)
                                <option value="{{ $commune->ward_code }}"
                                    {{ $chooseCommune == $commune->ward_code ? 'selected' : '' }}>
                                    {{ $commune->name }}
                                </option>
                            @endforeach
                        </select>
                        @if ($errors->first('commune_id'))
                            <div class="invalid-alert text-danger">{{ $errors->first('commune_id') }}</div>
                        @endif
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <input type="text" class="form-control {{ $errors->first('address') ? 'is-invalid' : '' }}"
                            name="address" placeholder="{{ trans('language.enter_address') }}"
                            value="{{ old('address') ?? ($user->address ?? '') }}"
                            {{ !$canEdit ? 'disabled' : '' }}>
                        @if ($errors->first('address'))
                            <div class="invalid-alert text-danger">{{ $errors->first('address') }}</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-3 col-md-3">
                <div class="form-group">
                    <label for="">{{ trans('language.birthday') }}</label>
                    <label class="input-group mb-0 {{ !$canEdit ? 'disabled' : '' }}">
                        <input type="text" class="form-control {{ $errors->first('birthday') ? 'is-invalid' : '' }}"
                            data-picker="date" autocomplete="off" name="birthday"
                            placeholder="{{ trans('language.enter_birthday') }}"
                            value="{{ old('birthday') ?? (isset($user->birthday) ? 
                            (new \App\Helpers\DateTimeHelper())->dateFormatLanguage($user->birthday, 'd/m/Y') : '') }}"
                            {{ !$canEdit ? 'disabled' : '' }}>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <img src="{{ asset('images/icon-calendar.svg') }}" />
                            </div>
                        </div>
                    </label>
                    @if ($errors->first('birthday'))
                        <div class="invalid-alert text-danger">{{ $errors->first('birthday') }}</div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
