export default function changeCSS(){
    if($('body').find('.time-start').length > 0){
        let height =  $('.time-start').height()/2 + 'px';
        document.querySelector(".time-start .triangle").style.setProperty("--widthTriangleStartTime", height)
        document.querySelector(".time-end .triangle").style.setProperty("--widthTriangleEndTime", height)
        $( window ).resize(function() {
            let height =  $('.time-start').height()/2 + 'px';
            document.querySelector(".time-start .triangle").style.setProperty("--widthTriangleStartTime", height)
            document.querySelector(".time-end .triangle").style.setProperty("--widthTriangleEndTime", height)
        });
    }
}