<?php

namespace App\Enums;

enum PaymentStatus: string
{
    case INIT      = 'init';       // Payment intent created
    case PENDING   = 'pending';    // Redirected / waiting callback
    case SUCCESS   = 'success';    // Confirmed paid
    case FAILED    = 'failed';     // Provider reports failure
    case EXPIRED   = 'expired';    // Not paid in time window
    case CANCELED  = 'canceled';   // User canceled
}
