<?php

namespace App\Enums;

enum SmsErrorEnum: int
{
    case EXCEPTION = -1;
    case SUCCESS = 0;
    case INVALID_AUTH = 1;
    case INVALID_SCHEDULE = 2;
    case INVALID_METHOD = 3;
    case INVALID_TEMPLATE = 7;
    case INVALID_TIME_QC = 8;
    case INVALID_CONTRACT_TYPE = 9;
    case INVALID_USERNAME = 10;
    case INVALID_MESSAGE_LENGTH = 11;
    case INVALID_TIME_POLICY = 12;
    case INVALID_CONTRACT = 13;
    case INVALID_LABEL = 14;
    case INVALID_AGENT = 15;
    case RATE_LIMIT = 16;
    case INVALID_ENCODING = 17;
    case CONTRACT_QUOTA_EXCEEDED = 20;
    case CUSTOMER_QUOTA_EXCEEDED = 21;
    case AGENT_QUOTA_EXCEEDED = 22;
    case MULTI_NETWORK_OR_INVALID_PHONE = 23;
    case SCHEDULE_TOO_EARLY = 24;
    case WRONG_NETWORK_OR_LABEL = 25;
    case TOO_MANY_QC_TODAY = 26;
    case INVALID_PARAM_VALUE = 27;
    case PARAM_LENGTH_EXCEEDED = 28;
    case BRANDNAME_EXPIRED = 29;
    case ILLEGAL_KEYWORD = 30;
    case INVALID_CUSTOMER_STATUS = 31;
    case DUPLICATE_REQUEST_ID = 33;

    public function getDescription(): string
    {
        return match($this) {
            self::EXCEPTION => 'Exception - Request contains special characters or internal error',
            self::SUCCESS => 'Success',
            self::INVALID_AUTH => 'Username, password, IP invalid - Contact media to check credentials',
            self::INVALID_SCHEDULE => 'Schedule time format invalid - Should be dd/MM/yyyy HH:mm',
            self::INVALID_METHOD => 'Method ID invalid',
            self::INVALID_TEMPLATE => 'Template invalid or not exists with label and agent',
            self::INVALID_TIME_QC => 'Invalid time for QC message - Only allowed 8h-11h30, 13h-18h30, 20h-21h',
            self::INVALID_CONTRACT_TYPE => 'Contract_type_id invalid - CSKH=1 or QC=2',
            self::INVALID_USERNAME => 'User_name invalid - Agent login username incorrect',
            self::INVALID_MESSAGE_LENGTH => 'Message length invalid',
            self::INVALID_TIME_POLICY => 'Time invalid with Vinaphone policy',
            self::INVALID_CONTRACT => 'Contract invalid - Contract status = 0',
            self::INVALID_LABEL => 'Label invalid - Label_id invalid or not activated',
            self::INVALID_AGENT => 'Agent invalid - Agent_id invalid or not activated',
            self::RATE_LIMIT => 'Send rate limit exceeded',
            self::INVALID_ENCODING => 'Character encoding invalid - Wrong dataencoding param or content encoding',
            self::CONTRACT_QUOTA_EXCEEDED => 'Contract message quota exceeded - Agent can extend limit',
            self::CUSTOMER_QUOTA_EXCEEDED => 'Customer message quota exceeded - Agent can extend limit',
            self::AGENT_QUOTA_EXCEEDED => 'Agent message quota exceeded - Contact VNP Admin',
            self::MULTI_NETWORK_OR_INVALID_PHONE => 'Multiple networks in one request or invalid phone number',
            self::SCHEDULE_TOO_EARLY => 'Schedule time earlier than current time - Allow send 1 day advance',
            self::WRONG_NETWORK_OR_LABEL => 'Wrong network or invalid label - Subscriber moved network, label not declared',
            self::TOO_MANY_QC_TODAY => 'Subscriber received 3 QC messages today',
            self::INVALID_PARAM_VALUE => 'Parameter value invalid',
            self::PARAM_LENGTH_EXCEEDED => 'Parameter length exceeded limit',
            self::BRANDNAME_EXPIRED => 'Brandname expired',
            self::ILLEGAL_KEYWORD => 'Message contains illegal keyword',
            self::INVALID_CUSTOMER_STATUS => 'Customer status invalid - No contract',
            self::DUPLICATE_REQUEST_ID => 'Duplicate request id - Only for bank API'
        };
    }

    public static function getDescriptionByCode(int $errorCode): string
    {
        foreach (self::cases() as $case) {
            if ($case->value === $errorCode) {
                return $case->getDescription();
            }
        }
        return 'Unknown error code: ' . $errorCode;
    }
}
