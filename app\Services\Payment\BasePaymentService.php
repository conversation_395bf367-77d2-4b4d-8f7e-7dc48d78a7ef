<?php

namespace App\Services\Payment;

use App\Enums\OrderStatus;
use App\Enums\PaymentEvent;
use App\Enums\PaymentChannel;
use App\Enums\PaymentStatus;
use App\Enums\TransactionStatus;
use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentLog;
use App\Models\PaymentTransaction;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

abstract class BasePaymentService
{

    /**
     * Find an existing reusable pending payment for the order and channel.
     * - Returns the latest pending payment if exists, otherwise null
     * - Used to reuse existing payments for retrying or resuming
     */
    protected function findReusablePendingPayment(Order $order, PaymentChannel $channel): ?Payment
    {
        $existing = $order->payments()
            ->where('channel', $channel->value)
            ->where('status', PaymentStatus::PENDING->value)
            ->where(function ($q) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>', now());
            })
            ->latest()->first();

        return $existing;
    }

    /**
     * Create a new payment for the order with the specified channel.
     * - Creates a new payment record with PENDING status
     * - Sets expires_at based on the provided Carbon instance
     * - Returns the created Payment model
     * - Always executed under transaction + row lock to avoid double-commit
     */
    protected function createPayment(Order $order, string $channel, Carbon $expiresAt, array $meta = []): Payment
    {
        return DB::transaction(function () use ($order, $channel, $expiresAt, $meta) {
            // Lock the order row to avoid concurrent attempts creation races
            $order = Order::whereKey($order->id)->lockForUpdate()->first();

            $attempt = ($order->payments()->where('channel', $channel)->max('attempt_no') ?? 0) + 1;

            $payment = $order->payments()->create([
                'channel'    => $channel,
                'status'     => PaymentStatus::PENDING->value,
                'amount'     => $order->amount,
                'currency'   => $order->currency,
                'attempt_no' => $attempt,
                'expires_at' => $expiresAt,
                'meta'       => $meta,
            ]);

            $this->log($payment, 'info', 'Payment created', ['attempt_no' => $attempt]);
            $this->tx($payment, PaymentEvent::REQUESTED->value, TransactionStatus::REQUESTED->value,
                null, null, ['meta' => $meta]);

            return $payment;
        });
    }

    /**
     * Mark payment as SUCCESS and update order to PAID.
     * - Updates payment status to SUCCESS
     * - Sets paid_at timestamp
     * - Updates order status to PAID if not already
     * - Logs the transaction and payment status change
     * - Returns the updated Payment model with refreshed order relation
     */
    protected function markSuccess(
        Payment $payment,
        array $providerPayload = [],
        ?string $providerTxnId = null,
        string $event = PaymentEvent::IPN->value
    ): Payment {
        return DB::transaction(function () use ($payment, $providerPayload, $providerTxnId, $event) {
            // Reload with lock (and lock the order as well)
            $payment = Payment::whereKey($payment->id)->lockForUpdate()->with('order')->first();
            $order   = Order::whereKey($payment->order_id)->lockForUpdate()->first();

            // Idempotency: already paid?
            if ($payment->status === PaymentStatus::SUCCESS) {
                $this->log($payment, 'info', 'Payment already SUCCESS (idempotent acknowledge)', ['event' => $event]);
                $this->tx(
                    $payment,
                    $event,
                    TransactionStatus::CONFIRMED->value,
                    Arr::get($providerPayload, 'code'),
                    Arr::get($providerPayload, 'message'),
                    $providerPayload
                );
                return $payment->fresh('order');
            }

            // Update payment/order to success
            $hasUpdatePaymentStatus = $this->hasUpdatePaymentStatus($event);
            $meta = $payment->meta ?? [];
            $meta['provider_return_status'] = PaymentStatus::SUCCESS->value;
            $payment->update(['meta' => $meta]);
            if ($hasUpdatePaymentStatus) {
                $meta['success_payload'] = $providerPayload;
                if ($providerTxnId) {
                    $meta['provider_transaction_id'] = $providerTxnId;
                }

                $payment->update([
                    'status'                   => PaymentStatus::SUCCESS,
                    'paid_at'                  => now(),
                    'provider_transaction_id'  => $providerTxnId ?: $payment->provider_transaction_id,
                    'meta'                     => $meta,
                ]);

                // If order already PAID, keep; otherwise set PAID
                if ($order->status !== OrderStatus::PAID) {
                    $order->update([
                        'status'  => OrderStatus::PAID,
                        'paid_at' => now(),
                    ]);
                }
            }

            $this->tx(
                $payment,
                $event,
                TransactionStatus::CONFIRMED->value,
                Arr::get($providerPayload, 'code'),
                Arr::get($providerPayload, 'message'),
                $providerPayload
            );
            $this->log($payment, 'info', 'Payment marked SUCCESS', ['event' => $event]);

            return $payment->fresh('order');
        });
    }

    /**
     * Mark payment as FAILED and update order to PENDING.
     * - Updates payment status to FAILED
     * - Keeps order status PENDING to allow retry
     * - Logs the transaction and payment status change
     * - Returns the updated Payment model with refreshed order relation
     */
    protected function markFailed(
        Payment $payment,
        ?string $code = null,
        ?string $message = null,
        array $payload = [],
        string $event = PaymentEvent::IPN->value
    ): Payment {
        return DB::transaction(function () use ($payment, $code, $message, $payload, $event) {
            $payment = Payment::whereKey($payment->id)->lockForUpdate()->with('order')->first();
            $order   = Order::whereKey($payment->order_id)->lockForUpdate()->first();

            // Idempotency: if already SUCCESS, do not downgrade
            if ($payment->status === PaymentStatus::SUCCESS) {
                $this->log(
                    $payment,
                    'info',
                    'Ignore FAILED because payment is already SUCCESS (idempotent)',
                    ['event' => $event, 'code' => $code]
                );
                $this->tx(
                    $payment,
                    $event,
                    TransactionStatus::CONFIRMED->value,
                    $code,
                    'Ignored, already SUCCESS',
                    $payload
                );
                return $payment->fresh('order');
            }

            // Update payment and order to failed
            $hasUpdatePaymentStatus = $this->hasUpdatePaymentStatus($event);
            $meta = $payment->meta ?? [];
            $meta['provider_return_status'] = PaymentStatus::FAILED->value;
            $payment->update(['meta' => $meta]);
            if ($hasUpdatePaymentStatus) {
                $meta['failed_payload'] = $payload;

                $payment->update([
                    'status' => PaymentStatus::FAILED,
                    'meta'   => $meta,
                ]);

                // Keep order pending to allow retry
                if ($order->status !== OrderStatus::PAID) {
                    $order->update(['status' => OrderStatus::PENDING]);
                }
            }

            $this->tx($payment, $event, TransactionStatus::FAILED->value, $code, $message, $payload);
            $this->log(
                $payment,
                'warning',
                'Payment marked FAILED',
                ['message' => $message] + $payload['context'] ?? []
            );

            return $payment->fresh('order');
        });
    }

    /**
     * Mark payment as EXPIRED and update order to PENDING.
     * - Updates payment status to EXPIRED
     * - Sets order status to PENDING if not already PAID
     * - Logs the transaction and payment status change
     * - Returns the updated Payment model with refreshed order relation
     */
    protected function markExpired(Payment $payment, array $payload = []): Payment
    {
        return DB::transaction(function () use ($payment, $payload) {
            $payment = Payment::whereKey($payment->id)->lockForUpdate()->with('order')->first();
            $order   = Order::whereKey($payment->order_id)->lockForUpdate()->first();

            if ($payment->status === PaymentStatus::SUCCESS) {
                // Do not expire a successful payment
                $this->log($payment, 'info', 'Skip expire: already SUCCESS');
                return $payment->fresh('order');
            }

            $payment->update(['status' => PaymentStatus::EXPIRED]);

            if ($order->status !== OrderStatus::PAID) {
                $order->update(['status' => OrderStatus::PENDING]);
            }

            $this->tx($payment, PaymentEvent::EXPIRE->value, TransactionStatus::EXPIRED->value,
                null, 'Expired', ['payload' => $payload]);
            $this->log($payment, 'info', 'Payment expired');
            return $payment->fresh('order');
        });
    }

    /**
     * Append a transaction entry (DB transaction table).
     * - Used to log payment events like requested, confirmed, failed, expired
     * - Includes request/response payloads and signature validity
     * - Returns void
     */
    protected function tx(
        Payment $payment,
        string $event,
        string $status,
        ?string $code,
        ?string $msg,
        array $res = []
    ): void {
        PaymentTransaction::create([
            'payment_id'        => $payment->id,
            'event'             => $event,
            'status'            => $status,
            'provider_code'     => $code,
            'provider_message'  => $msg,
            'signature_valid'   => (bool)($res['signature_valid'] ?? true),
            'request_payload'   => $res['request'] ?? null,
            'response_payload'  => $res['payload'] ?? $res['response'] ?? null,
        ]);
    }

    /**
     * Log a message for the payment.
     * - Creates a new PaymentLog entry with the specified level, message, and context
     * - Used for debugging and tracking payment events
     * - Returns void
     */
    protected function log(Payment $payment, string $level, string $message, array $context = []): void
    {
        PaymentLog::create([
            'payment_id' => $payment->id,
            'level'      => $level,
            'message'    => $message,
            'context'    => $context,
        ]);
    }

    /**
     * Ensure idempotency by checking if a payment with the same provider transaction ID already exists.
     * - If exists, returns the existing payment
     * - If not, returns the provided payment
     * - Used to prevent duplicate processing of the same transaction
     */
    protected function ensureIdempotentByProviderTxn(?string $providerTxnId, Payment $payment): Payment
    {
        if (!$providerTxnId) {
            return $payment;
        }

        $existing = Payment::query()
            ->where('provider_transaction_id', $providerTxnId)
            ->where('status', PaymentStatus::SUCCESS->value)
            ->first();

        return $existing ?: $payment;
    }

    /**
     * Check if the payment is actually expired based on its status and expiration time.
     * - Returns true if payment is PENDING and expires_at is in the past
     * - Used to determine if we need to mark the payment as expired
     */
    public function expire(Payment $payment): Payment
    {
        if ($this->isActuallyExpired($payment)) {
            return $this->markExpired($payment);
        }
        return $payment->refresh();
    }

    /**
     * Check if the payment is actually expired.
     * - Returns true if payment is PENDING and expires_at is in the past
     * - Used internally to determine if we need to mark the payment as expired
     */
    protected function isActuallyExpired(Payment $payment): bool
    {
        return $payment->status === PaymentStatus::PENDING
            && $payment->expires_at
            && now()->greaterThan($payment->expires_at);
    }

    /**
     * Check if the payment status should be updated based on the event type.
     * - In production, only IPN events can update payment status
     * - In non-production environments, all events can update status
     * - Returns true if the event allows updating payment status
     */
    protected function hasUpdatePaymentStatus(string $event): bool
    {
        $hasUpdatePaymentStatus = !app()->environment('production')
            || $event === PaymentEvent::IPN->value;

        return $hasUpdatePaymentStatus;
    }
}
