#loader {
    font-size: 10px;
    width: 1em;
    height: 1em;
    border-radius: 50%;
    position: absolute;
    text-indent: -9999em;
    animation: mulShdSpin 1.1s infinite ease;
    transform: translateZ(0);
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

@keyframes mulShdSpin {
    0%,
    100% {
        box-shadow: 0em -2.6em 0em 0em #ffc107, 1.8em -1.8em 0 0em rgba(255,193,7, 0.2), 2.5em 0em 0 0em rgba(255,193,7, 0.2), 1.75em 1.75em 0 0em rgba(255,193,7, 0.2), 0em 2.5em 0 0em rgba(255,193,7, 0.2), -1.8em 1.8em 0 0em rgba(255,193,7, 0.2), -2.6em 0em 0 0em rgba(255,193,7, 0.5), -1.8em -1.8em 0 0em rgba(255,193,7, 0.7);
    }
    12.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(255,193,7, 0.7), 1.8em -1.8em 0 0em #ffc107, 2.5em 0em 0 0em rgba(255,193,7, 0.2), 1.75em 1.75em 0 0em rgba(255,193,7, 0.2), 0em 2.5em 0 0em rgba(255,193,7, 0.2), -1.8em 1.8em 0 0em rgba(255,193,7, 0.2), -2.6em 0em 0 0em rgba(255,193,7, 0.2), -1.8em -1.8em 0 0em rgba(255,193,7, 0.5);
    }
    25% {
        box-shadow: 0em -2.6em 0em 0em rgba(255,193,7, 0.5), 1.8em -1.8em 0 0em rgba(255,193,7, 0.7), 2.5em 0em 0 0em #ffc107, 1.75em 1.75em 0 0em rgba(255,193,7, 0.2), 0em 2.5em 0 0em rgba(255,193,7, 0.2), -1.8em 1.8em 0 0em rgba(255,193,7, 0.2), -2.6em 0em 0 0em rgba(255,193,7, 0.2), -1.8em -1.8em 0 0em rgba(255,193,7, 0.2);
    }
    37.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(255,193,7, 0.2), 1.8em -1.8em 0 0em rgba(255,193,7, 0.5), 2.5em 0em 0 0em rgba(255,193,7, 0.7), 1.75em 1.75em 0 0em #ffc107, 0em 2.5em 0 0em rgba(255,193,7, 0.2), -1.8em 1.8em 0 0em rgba(255,193,7, 0.2), -2.6em 0em 0 0em rgba(255,193,7, 0.2), -1.8em -1.8em 0 0em rgba(255,193,7, 0.2);
    }
    50% {
        box-shadow: 0em -2.6em 0em 0em rgba(255,193,7, 0.2), 1.8em -1.8em 0 0em rgba(255,193,7, 0.2), 2.5em 0em 0 0em rgba(255,193,7, 0.5), 1.75em 1.75em 0 0em rgba(255,193,7, 0.7), 0em 2.5em 0 0em #ffc107, -1.8em 1.8em 0 0em rgba(255,193,7, 0.2), -2.6em 0em 0 0em rgba(255,193,7, 0.2), -1.8em -1.8em 0 0em rgba(255,193,7, 0.2);
    }
    62.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(255,193,7, 0.2), 1.8em -1.8em 0 0em rgba(255,193,7, 0.2), 2.5em 0em 0 0em rgba(255,193,7, 0.2), 1.75em 1.75em 0 0em rgba(255,193,7, 0.5), 0em 2.5em 0 0em rgba(255,193,7, 0.7), -1.8em 1.8em 0 0em #ffc107, -2.6em 0em 0 0em rgba(255,193,7, 0.2), -1.8em -1.8em 0 0em rgba(255,193,7, 0.2);
    }
    75% {
        box-shadow: 0em -2.6em 0em 0em rgba(255,193,7, 0.2), 1.8em -1.8em 0 0em rgba(255,193,7, 0.2), 2.5em 0em 0 0em rgba(255,193,7, 0.2), 1.75em 1.75em 0 0em rgba(255,193,7, 0.2), 0em 2.5em 0 0em rgba(255,193,7, 0.5), -1.8em 1.8em 0 0em rgba(255,193,7, 0.7), -2.6em 0em 0 0em #ffc107, -1.8em -1.8em 0 0em rgba(255,193,7, 0.2);
    }
    87.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(255,193,7, 0.2), 1.8em -1.8em 0 0em rgba(255,193,7, 0.2), 2.5em 0em 0 0em rgba(255,193,7, 0.2), 1.75em 1.75em 0 0em rgba(255,193,7, 0.2), 0em 2.5em 0 0em rgba(255,193,7, 0.2), -1.8em 1.8em 0 0em rgba(255,193,7, 0.5), -2.6em 0em 0 0em rgba(255,193,7, 0.7), -1.8em -1.8em 0 0em #ffc107;
    }
}

.loader{
    min-height: calc(100vh - 265px);
    position: relative;
}