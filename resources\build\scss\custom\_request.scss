.detail-request{
    .title-tree__title{
        font-size: 1.2rem;
    }
    #task-action{
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        @media (max-width: 768px) {
            float: left;
        }

        @media (min-width: 769px) {
            float: right;
        }
    }
}
.infomation-request {
    .info-title {
        color: black;
        font-size: 1rem;
        border-bottom: 3px solid #dee2e6;
        font-weight: 700;
        text-transform: uppercase;
    }
    .content{
        margin-top: 15px;
        margin-bottom: 30px;
        table{
            tr{
                td{
                    border: none;
                    padding: 5px;
                    &:nth-child(1) {
                        color: #dee2e6;
                    }
                    &:nth-child(2) {
                        color: #888;
                    }
                }
            }
        }
    }
    .content.time-line{
        .icon{
            width: 100%;
            align-content: center;
            border: 1px solid #dee2e6!important;
            border-radius: 50%;
            width: 2.8rem;
            height: 2.8rem;
            font-size: 15px !important;
            min-width: 2.8rem !important;
            z-index: 2;
            position: relative;

            /* Extra large devices (large laptops and desktops, 1200px and up) */
            @media (min-width: 1200px) and (max-width:1500px) {
                width: 1.9rem;
                height: 1.9rem;
                min-width: 1.9rem !important;
            }
            @media (max-width:450px) {
                width: 2.3rem;
                height: 2.3rem;
                min-width: 2.3rem !important;
            }
        }
        .time-request{
            display: flex;
            justify-content: space-between;
            min-height: 70px;
            .icon-request {
                position: relative;
                display: flex;
                padding-left: 5px;
            }
            .icon-request::before {
                content: "";
                position: absolute;
                width: 1px;
                background-color: #dee2e6;
                top: 0;
                bottom: 0;
                left: calc(50% + 1px);
                margin: auto;
                z-index: 1;
            }
            &:last-child {
                .icon-request::before {
                    width: 0;
                }
            }
            @media(max-width: 1199px){
                justify-content: unset;
                .action-request{
                    padding-top: 12px;
                }
                .time{
                    padding-top: 5px;
                }
                .icon-request{
                    margin: 0 10px;
                }
            }
        }
    }
    .avatar-request{
        height: 60px;
        padding-left: 5px
    }
}
