<div class="card card-profile" id="basic_information">
    <div class="card-header">
        {{ trans('language.user_basic_information') }}
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <div class="text-center">
                        <div class="form-image">
                            <img src="{{ isset($user) ? route('user.avatar', ['id' => $user->id])
                                : asset('images/user-default.png') }}" class="form-image__view"
                                id="avatar_view" alt="preview image">
                            <input type="file" class="form-image__file" id="avatar" name="avatar"
                                accept=".png, .jpg, .jpeg, .gif"
                                data-origin="{{ isset($user) ? route('user.avatar', ['id' => $user->id])
                                : asset('images/user-default.png') }}" 
                                {{ isset($deleted) && $deleted == true ? 'disabled' : '' }}>
                            <label for="avatar" class="form-image__label"><i class="fas fa-pen"></i></label>
                        </div>
                        @if ($errors->first('avatar'))
                            <div class="invalid-alert text-danger">{{ $errors->first('avatar') }}</div>
                        @endif
                    </div>
                </div>
                <div class="user-short__info">
                    <p>
                        <a href="mailto:{{ $user->email ?? '' }}" title="{{ $user->email ?? '' }}">
                            {{ $user->email ?? '' }}
                        </a>
                    </p>
                    @if (isset($user->department))
                        <p>{{ $user->department->name }}</p>
                    @endif
                    @if (isset($user->position))
                        <p class="blue">{{ $user->position->name }}</p>
                    @endif
                </div>
            </div>
            <div class="col-md-8">
                <div class="row">
                    <div class="col-sm-6 col-md-6">
                        <div class="form-group">
                            <label for="">{{ trans('language.type_account') }} <span
                                    class="text-red">*</span></label>
                            @php
                                $chooseRole = old('role') ?? ($userRoles ?? []);
                            @endphp
                            <select class="select2-base {{ $errors->first('role') ? 'is-invalid' : '' }}"
                                name="role[]" style="width: 100%" {{ !$canEdit ? 'disabled' : '' }} multiple>
                                <option value=""
                                    {{ empty($chooseRole) ? 'selected' : '' }}>
                                    {{ trans('language.user') }}</option>
                                @foreach ($allowedRoles as $role)
                                    <option value="{{ $role->name }}"
                                        {{ in_array($role->name, $chooseRole) ? 'selected' : '' }}>
                                        {{ trans('language.' . $role->name) }}
                                    </option>
                                @endforeach
                            </select>
                            @if ($errors->first('role'))
                                <div class="invalid-alert text-danger">{{ $errors->first('role') }}</div>
                            @endif
                        </div>
                    </div>
                    <div class="col-sm-6 col-md-6">
                        <div class="form-group">
                            <label for="">{{ trans('language.full_name') }} <span
                                    class="text-red">*</span></label>
                            <input type="text" class="form-control {{ $errors->first('name') ? 'is-invalid' : '' }}"
                                name="name" placeholder="{{ trans('language.enter_full_name') }}" required
                                value="{{ old('name') ? old('name') : (isset($user->name) ? $user->name : '') }}"
                                {{ !$canEdit ? 'disabled' : '' }}>
                            @if ($errors->first('name'))
                                <div class="invalid-alert text-danger">{{ $errors->first('name') }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="">{{ trans('language.email') }} <span class="text-red">*</span></label>
                            <input type="email"
                                class="form-control {{ $errors->first('email') ? 'is-invalid' : '' }}" name="email"
                                placeholder="{{ trans('language.enter_email') }}" required
                                value="{{ old('email') ? old('email') : (isset($user->email) ? $user->email : '') }}"
                                {{ !$canEdit ? 'disabled' : '' }}>

                            @if ($errors->first('email'))
                                <div class="invalid-alert text-danger">{{ $errors->first('email') }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="">{{ trans('language.phone') }}</label>
                            <input type="text"
                                class="form-control {{ $errors->first('phone') ? 'is-invalid' : '' }}" name="phone"
                                placeholder="{{ trans('language.enter_phone') }}"
                                value="{{ old('phone') ? old('phone') : (isset($user->phone) ? $user->phone : '') }}"
                                {{ !$canEdit ? 'disabled' : '' }}>

                            @if ($errors->first('phone'))
                                <div class="invalid-alert text-danger">{{ $errors->first('phone') }}</div>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="radioGender1">{{ trans('language.gender') }}</label>
                            <br>
                            @php
                                $genders = trans('language.genders');
                                $choose_gender = old('gender')
                                    ? old('gender')
                                    : (isset($user->gender)
                                        ? $user->gender
                                        : 0);
                            @endphp
                            @for ($i = 0; $i < count($genders); $i++)
                                <div class="icheck-primary d-inline mr-4">
                                    <input type="radio" name="gender" id="radioGender{{ $i }}"
                                        value="{{ $i }}" {{ $i === $choose_gender ? 'checked' : '' }}
                                        {{ !$canEdit ? 'disabled' : '' }}>
                                    <label for="radioGender{{ $i }}">
                                        {{ $genders[$i] }}
                                    </label>
                                </div>
                            @endfor
                        </div>
                    </div>
                </div>
                @if ($canEdit)
                    <div id="passwordSection">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="">{{ trans('language.password') }}</label>
                                    <div class="input-group">
                                        <input id="pw" type="password"
                                            class="form-control {{ $errors->has('password') ? 'is-invalid' : '' }}"
                                            name="password" placeholder="{{ trans('language.enter_password') }}"
                                            autocomplete="new-password">
                                        <div class="input-group-append" id="togglePw" style="cursor: pointer;">
                                            <div class="input-group-text">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                        </div>
                                    </div>
                                    @if ($errors->first('password'))
                                        <div class="invalid-alert text-danger mt-1">{{ $errors->first('password') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="d-block">&nbsp;</label>
                                    <button type="button" id="generatePw" class="btn btn-primary">
                                        {{ trans('language.generate_password') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
