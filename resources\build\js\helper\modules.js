export function placeCursorAtEnd(){
    $.fn.extend({
        placeCursorAtEnd: function() {
            // Places the cursor at the end of a contenteditable container (should also work for textarea / input)
            if (this.length === 0) {
                throw new Error("Cannot manipulate an element if there is no element!");
            }
            let el = this[0];
            let range = document.createRange();
            let sel = window.getSelection();
            let childLength = el.childNodes.length;
            if (childLength > 0) {
                let lastNode = el.childNodes[childLength - 1];
                let lastNodeChildren = lastNode.childNodes.length;
                range.setStart(lastNode, lastNodeChildren);
                range.collapse(true);
                sel.removeAllRanges();
                sel.addRange(range);
            }
            return this;
        }
    });
}