<?php

namespace App\Http\Requests;

use App\Enums\PaymentChannel;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StartPaymentRequest extends FormRequest
{
    public function authorize(): bool { return true; }

    public function rules(): array
    {
        return [
            'order_id' => ['required','uuid','exists:orders,id'],
            'channel'  => ['required', new Enum(PaymentChannel::class)],
        ];
    }
}
