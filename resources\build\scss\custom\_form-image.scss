.form-image {
    display: inline-block;
    position: relative;

    &__view {
        width: 208px;
        height: 208px;
        border: 1px solid #ddd;
        overflow: hidden;
        object-fit: cover;
        padding: 3px;
        display: block;
        border-radius: 50%;
    }

    &__file {
        width: 1px;
        height: 1px;
        display: block;
        opacity: 0;
        position: absolute;
        right: 19px;
        border: 0;

        &[disabled], &.disabled {
            + label {
                cursor: no-drop;

                &:hover, &:focus {
                    background: #fff;
                    color: $gray-600;
                }
            }
        }
    }

    &__label {
        cursor: pointer;
        box-shadow: 0 0 27px 0;
        margin: 0;
        width: 30px;
        height: 30px;
        line-height: 30px;
        border-radius: 50%;
        color: $gray-600;
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: #fff;
        transition: all linear .2s;

        &:hover, &:focus {
            background: $color-bee;
            color: #fff;
        }
    }
}

img {
    max-width: 100%;
}