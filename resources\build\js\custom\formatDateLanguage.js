export function formatDateLanguage(language){
    let formatDateEvent = 'dd/mm/yyyy',
        formatTimeEvent = 'H:i',
        formatDateTimeEvent ='dd/mm/yyyy H:i';
    switch (language){
        case 'vi':
            formatDateEvent= 'dd/mm/yyyy';
            formatTimeEvent = 'H:i';
            formatDateTimeEvent ='dd/mm/yyyy H:i';
            break;
        case 'en':
            formatDateEvent = 'yyyy-mm-dd';
            formatTimeEvent = 'H:i';
            formatDateTimeEvent ='yyyy-mm-dd H:i';
            break;
        case 'ja':
            formatDateEvent = 'yyyy年mm月dd日';
            formatTimeEvent = 'H時i分';
            formatDateTimeEvent ='yyyy年mm月dd日 H時i分';
            break;
        default:
            break;
    }
    return [formatDateEvent,formatTimeEvent,formatDateTimeEvent]
}

export function getDateFormats(language) {
    switch (language) {
        case 'vi':
            return {
                formatDate: 'dd/mm/yyyy',
                formatMonth: 'mm/yyyy',
                formatTime: 'HH:mm:ss',
                formatDateTime: 'DD/MM/YYYY HH:mm'
            };
        case 'en':
            return {
                formatDate: 'yyyy-mm-dd',
                formatMonth: 'yyyy-mm',
                formatTime: 'HH:mm:ss',
                formatDateTime: 'YYYY-MM-DD HH:mm'
            };
        case 'ja':
            return {
                formatDate: 'yyyy年mm月dd日',
                formatMonth: 'yyyy年mm月',
                formatTime: 'HH時mm分ss秒',
                formatDateTime: 'YYYY年MM月DD日 HH時mm分'
            };
        default:
            return {
                formatDate: 'dd/mm/yyyy',
                formatMonth: 'mm/yyyy',
                formatTime: 'HH:mm:ss',
                formatDateTime: 'DD/MM/YYYY HH:mm'
            };
    }
}