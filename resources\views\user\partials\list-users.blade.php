@if (isset($users) && count($users) > 0)
<div class="table-projects table-responsive" id="double-scroll">
    <table class="table table-hover table-valign-middle min-width-1200">
        <thead class="text-center text-nowrap">
        <tr>
            <th>{{ trans('language.id') }}</th>
            <th width = "60px">{{ trans('language.image') }}</th>
            <th>{{ trans('language.full_name') }}</th>
            <th>{{ trans('language.email') }}</th>
            <th>{{ trans('language.gender') }}</th>
            <th>{{ trans('language.address') }}</th>
            <th>{{ trans('language.phone') }}</th>
            <th width = "120px">{{ trans('language.operation') }}</th>
        </tr>
        </thead>
        <tbody>
            @foreach($users as $idx => $user)
                <tr>
                    <td class="text-center">{{$user->id}}</td>
                    <td class="text-center">
                        <a href="{{ route('user.edit',['user'=>$user]) }}" class="fancybox2"
                            data-fancybox-group="avatar-employees" title="ID: {{$user->id}} - {{ $user->name }}">
                            <img src="{{ route('user.avatar', ['id'=>$user->id]) }}"
                                 alt="{{ $user->name }} Avatar"
                                 class="avatar-employees">
                        </a>
                    </td>
                    <td>{{ $user->name }}</td>
                    <td>
                        <a href="mailto:{{$user->email}}" class="text-dark">{{$user->email}}</a>
                    </td>
                    <td class="text-center">{{ trans('language.genders')[$user->gender] ?? ''}}</td>
                    <td title="{{$user->address}}">{{$user->address}}</td>
                    <td class="text-center"><a href="tel:{{$user->phone}}" class="text-nowrap text-dark">{{(new \App\Helpers\StringHelper())->phoneNumberFormat($user->phone)}}</a></td>
                    <td class="text-center text-nowrap">
                        @if($user->deleted_at == null)
                            <a href="{{ route('user.edit',['user'=>$user]) }}" data-toggle='tooltip' title="{{trans('language.edit')}}" class="text-md text-primary mr-2"><i class="far fa-pen-alt"></i></a>
                            <a href="{{ route('user.destroy', ['user'=>$user]) }}"
                               data-toggle='tooltip'
                               title="{{trans('language.delete')}}"
                               class="text-md text-danger delete-row-table btn-delete-jb"
                               data-id="{{ $user->id }}"
                               data-title="{{trans('language.delete_user')}}"
                               data-text="<span class='text-bee'>ID: {{$user->id}}</span> - <strong>{{ $user->name }}</strong>"
                               data-url="{{ route('user.destroy', ['user'=>$user]) }}"
                               data-method="DELETE"
                               data-icon="question"><i class="far fa-trash-alt"></i></a>
                        @else
                            {{-- <a href="{{ route('user.edit',['user'=>$user]) }}" data-toggle='tooltip' title="{{trans('language.information')}}" class="text-md text-success mr-2"><i class="far fa-eye"></i></a>
                            <a href="{{ route('admin.user.restore', ['id'=>$user->id]) }}"
                               data-toggle='tooltip'
                               title="{{trans('language.restore')}}"
                               class="text-md text-warning delete-row-table"
                               data-id="{{ $user->id }}"
                               data-title="{{ trans('language.restore_employee') }}"
                               data-text="<span class='text-bee'>ID: {{$user->id}}</span> - <strong>{{ $user->name }}</strong>"
                               data-method="POST"
                               data-url="{{ route('admin.user.restore', ['id'=>$user->id]) }}"
                               data-icon="question"><i class="far fa-redo"></i></a> --}}
                        @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
<div class="pb-4">
    {{ $users->appends($request->query())->links('partials.pagination') }}
</div>
@else
    @include('partials.no-data-found')
@endif
