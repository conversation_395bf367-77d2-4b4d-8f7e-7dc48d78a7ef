<?php

namespace App\Http\Controllers\Api;

use App\Enums\OrderStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\StartPaymentRequest;
use App\Models\Order;
use App\Services\Payment\PaymentServiceFactory;
use Symfony\Component\HttpFoundation\Response;

class PaymentController extends Controller
{
    /**
     * Start a new payment attempt for the given order.
     *
     * @param StartPaymentRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function start(StartPaymentRequest $request)
    {
        $order = Order::findOrFail($request->order_id);

        // Block if already paid
        if ($order->status === OrderStatus::PAID) {
            return response()->json(['error' => __('message.order_already_paid')], Response::HTTP_BAD_REQUEST);
        }

        // If latest payment expired/failed, we'll create new attempt
        $service = PaymentServiceFactory::make($request->input('channel'));
        $payment = $service->initiate($order);

        return response()->json([
            'message' => __('message.success'),
            'data' => [
                'payment_url' => $service->buildRedirectUrl($payment)
            ]
        ]);
    }

    /**
     * Retry a payment attempt for the given order.
     *
     * @param StartPaymentRequest $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function retry(StartPaymentRequest $request)
    {
        $order   = Order::findOrFail($request->order_id);
        $service = PaymentServiceFactory::make($request->input('channel'));

        // business rule: allow retry only if order not paid
        return response()->json(['error' => __('message.order_already_paid')], Response::HTTP_BAD_REQUEST);

        $payment = $service->initiate($order);
        return redirect()->away($service->buildRedirectUrl($payment));
    }
}
