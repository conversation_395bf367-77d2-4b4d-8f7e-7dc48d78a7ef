<?php

namespace App\Logics;

use App\Helpers\StringHelper;
use Illuminate\Http\Request;
use App\Models\AppVersion;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class VersionManager
{
    const CACHE_TTL = 86400; // 1 day in seconds
    const APP_VERSION_CACHE = 'app_version:latest:';
    const PLATFORMS = [AppVersion::IOS, AppVersion::ANDROID];

    //Api Logic
    /**
     * Check version for API response
     *
     * @param int $platform
     * @param string $currentVersion
     * @return array
     */
    public function checkVersion(int $platform, string $currentVersion): array
    {
        $latestVersion = $this->getLatestVersionCached($platform);

        if (!$latestVersion) {
            return [
                'current_version' => $currentVersion,
                'latest_version' => null,
                'has_new_version' => false,
                'force_update' => false,
                'mode' => AppVersion::MODE_PREVIEW,
                'published_at' => null,
                'description' => null
            ];
        }

        $comparison = version_compare($currentVersion, $latestVersion->version);

        return [
            'current_version' => $currentVersion,
            'latest_version' => $latestVersion->version,
            'has_new_version' => $comparison < 0,
            'force_update' => $comparison < 0 ? $latestVersion->force_update : false,
            'mode' => $comparison > 0 ? AppVersion::MODE_PREVIEW : AppVersion::MODE_PRODUCTION,
            'published_at' => $latestVersion->published_at ? $latestVersion->published_at->toDateTimeString() : null,
            'description' => $latestVersion->description
        ];
    }

    /**
     * Generate cache key for platform
     *
     * @param int $platform
     * @return string
     */
    private function getCacheKey(int $platform): string
    {
        return self::APP_VERSION_CACHE . $platform;
    }

    /**
     * Get latest published version with cache
     *
     * @param int $platform
     * @return AppVersion|null
     */
    private function getLatestVersionCached(int $platform): ?AppVersion
    {
        $key = $this->getCacheKey($platform);

        return Cache::remember($key, self::CACHE_TTL, function () use ($platform) {
            return $this->latestPublishedVersion($platform);
        });
    }

    /**
     * Clear all version cache completely
     *
     * @return void
     */
    public function clearAllVersionCache(): void
    {
        $deletedCount = 0;

        foreach (self::PLATFORMS as $platform) {
            $key = $this->getCacheKey($platform);
            if (Cache::forget($key)) {
                $deletedCount++;
            }
        }

        Log::debug('[AppVersionService][clearAllVersionCache] Deleted ' . $deletedCount . ' version cache keys');
    }

    /**
     * Get latest published version for platform without cache
     * Supports flexible version formats: 1.2, 1.2.3, 1.2.4.5.6, etc.
     *
     * @param int $platform
     * @return AppVersion|null
     */
    private function latestPublishedVersion(int $platform): ?AppVersion
    {
        // Get only the IDs and versions for comparison
        $versions = AppVersion::published()
            ->platform($platform)
            ->select('id', 'version')
            ->get();

        if ($versions->isEmpty()) {
            return null;
        }

        // Find the latest published version ID
        $latest = null;
        foreach ($versions as $version) {
            if ($latest === null) {
                $latest = $version;
                continue;
            }

            $compare = version_compare($version->version, $latest->version);
            if (
                $compare > 0 ||
                ($compare === 0 && $version->id > $latest->id)
            ) {
                $latest = $version;
            }
        }

        return AppVersion::find($latest->id);
    }

    // Web Logic
    /**
     * Get all versions with pagination and filters for admin panel
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getAllVersions(Request $request): \Illuminate\Database\Eloquent\Builder
    {
        $query = AppVersion::query();
        $stringHelper = new StringHelper();

        // Apply search filter
        if ($request->has('search')) {
            $searchTerm = $stringHelper->formatStringWhereLike($request->search);
            $query->where(function ($q) use ($searchTerm) {
                $q->where('version', 'like', '%' . $searchTerm . '%')
                    ->orWhere('description', 'like', '%' . $searchTerm . '%')
                    ->orWhere('release_notes', 'like', '%' . $searchTerm . '%');
            });
        }

        // Apply platform filter
        if (isset($request->platform) && $request->platform !== '') {
            $query->where('platform', (int) $request->platform);
        }

        // Apply status filter
        if (isset($request->status) && $request->status !== '') {
            $query->where('status', (int) $request->status);
        }

        // Default sort by created_at desc
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Get latest versions for both platforms
     *
     * @return array
     */
    public function getLatestVersions(): array
    {
        return [
            'ios' => $this->latestPublishedVersion(AppVersion::IOS),
            'android' => $this->latestPublishedVersion(AppVersion::ANDROID)
        ];
    }

    /**
     * Create new version
     *
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function createVersion(Request $request): array
    {
        DB::beginTransaction();
        try {
            if ($request->input('platforms') == AppVersion::ANDROID_AND_IOS) {
                $platforms = [AppVersion::IOS, AppVersion::ANDROID];
            } else {
                $platforms = [$request->input('platforms')];
            }

            foreach ($platforms as $platform) {
                $versionData = [
                    'version' => $request->input('version'),
                    'platform' => (int) $platform,
                    'status' => (int) $request->input('status'),
                    'description' => $request->input('description'),
                    'release_notes' => $request->input('release_notes'),
                    'force_update' => $request->has('force_update'),
                    'published_at' => $request->input('status') == AppVersion::PUBLISHED ? now() : null,
                ];

                AppVersion::create($versionData);
            }

            // Clear cache
            $this->clearAllVersionCache();

            $platformName = collect($platforms)->map(function ($platform) {
                return $platform == AppVersion::IOS ? __('language.ios') : __('language.android');
            })->join(__('message.version_connector'));

            DB::commit();

            return [
                'platform_name' => $platformName,
                'version' => $request->input('version')
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update version
     *
     * @param int $id
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function updateVersion(int $id, Request $request): array
    {
        DB::beginTransaction();
        try {
            $version = AppVersion::findOrFail($id);

            $versionData = [
                'version' => $request->input('version'),
                'platform' => $request->input('platforms'),
                'status' => (int) $request->input('status'),
                'description' => $request->input('description'),
                'release_notes' => $request->input('release_notes'),
                'force_update' => $request->has('force_update'),
                'published_at' => $request->input('status') == AppVersion::PUBLISHED ?
                    ($version->published_at ?? now()) : null,
            ];

            // Update version
            $version->update($versionData);

            // Clear cache
            $this->clearAllVersionCache();

            // Get platform name
            $platformName = $version->platform == AppVersion::IOS ? __('language.ios') : __('language.android');

            DB::commit();

            return [
                'version' => $version->version,
                'platform_name' => $platformName
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
