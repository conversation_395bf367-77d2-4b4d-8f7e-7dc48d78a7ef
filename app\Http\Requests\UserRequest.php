<?php

namespace App\Http\Requests;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|max:200',
            'email' => 'required|email|max:200|unique:users,email,' . $this->user,
            'prefecture_id' => 'nullable|exists:provinces,province_code',
            'district_id' => 'nullable|exists:wards,ward_code',
            'address' => 'max:500',
            'password' => 'nullable|min:8|max:20',
            'birthday' => 'nullable|date_format:d/m/Y',
            'phone' => 'nullable|digits_between:10,11|unique:users,phone,' . $this->user,
            'avatar' => 'mimes:jpeg,png,jpg|max:10240',
            'gender' => 'in:0,1,2',
        ];

        // If the user is the last system manager, cannot remove this role
        if ($this->user && $this->user == auth()->id()
            && User::role(Role::ROLE_SYSTEM_MANAGER)->count() == 1
            && !in_array(Role::ROLE_SYSTEM_MANAGER, $this->role)
        ) {
            $rules['role'] = function ($attribute, $value, $fail) {
                return $fail(trans('validation.custom.required_exist_system_manager_user'));
            };
        } else {
            // If the user is a system manager, they cannot change their role
            $isAdmin = auth()->user()->hasRole(Role::ROLE_SYSTEM_MANAGER);
            $allowedRoles = Role::select('name')
                ->when(!$isAdmin, function ($q) {
                    $q->where('name', '!=', Role::ROLE_SYSTEM_MANAGER);
                })
                ->pluck('name')
                ->toArray();
            $rules['role'] = 'array|in:"",' . implode(",", $allowedRoles);
        }

        return $rules;
    }
}
