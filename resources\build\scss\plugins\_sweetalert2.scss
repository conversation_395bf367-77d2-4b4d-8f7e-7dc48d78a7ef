//
// Plugin: SweetAlert2
//

// Icon Colors
.swal2-icon {
  &.swal2-info {
    border-color: ligthen($info, 20%);
    color: $info;
  }

  &.swal2-warning {
    border-color: ligthen($warning, 20%);
    color: $warning;
  }

  &.swal2-error {
    border-color: ligthen($danger, 20%);
    color: $danger;
  }

  &.swal2-question {
    border-color: ligthen($secondary, 20%);
    color: $secondary;
  }

  &.swal2-success {
    border-color: ligthen($success, 20%);
    color: $success;

    .swal2-success-ring {
      border-color: ligthen($success, 20%);
    }

    [class^='swal2-success-line'] {
      background-color: $success;
    }
  }
}

.dark-mode {
  .swal2-popup {
    background-color: $dark;
    color: $gray-200;

    .swal2-content,
    .swal2-title {
      color: $gray-200;
    }
  }
}

.swal2-styled{
  padding: .35em 1em !important;
  font-size: 1em !important;
  &.swal2-confirm{
    background-color: #0791A3 !important;
  }
  &:focus{
    box-shadow: 0 0 0 3px rgba(#088493, 0.3) !important;
  }
  &.swal2-cancel{
    background-color: #FFFFFF !important;
    border: 1px solid #DDDFE1 !important;
    color: #000000 !important;
  }
  &:focus{
    box-shadow: 0 0 0 3px rgba(#FAFAFA, 0.3) !important;
  }
}

.swal2-content{
  font-size: 1em !important;
}

.swal2-title{
  font-size: 1.4em !important;
}