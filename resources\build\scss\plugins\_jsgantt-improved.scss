.gtaskname div, .gtaskname{
  font-size: 13px;
  min-width: 100%;
  max-width: 100%;
  width: 170px;
  line-height: 1.5;
  display: block;
  border: none;

  .gselector{
    font-size: 10px;
    line-height: 2;
  }

}
td.gtaskname {
  height: 24px;
}
.gcharttable tfoot{
  display: none;
}

span.gformlabel:hover, span.gselected{
  background: none;
  border: 0;
  padding: 0px 5px 2px;
  border-bottom: 2px solid #FCCA33;
}

.gtaskheading, .gmajorheading, .gminorheading{
  font-size: 11px;
}

.gtaskcellwkend, .gtaskcellcurrent, .gminorheadingwkend{
  font-size: 11px;
  background: #f9f9f9;
}

.footerdays{
  .gtaskheading, .gmajorheading, .gminorheading,
  .gtaskcellwkend, .gtaskcellcurrent, .gminorheadingwkend{
    font-weight: normal;
  }
}
.gmain{
  user-select: none; /* supported by Chrome and Opera */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
}

.gmainleft {
  position: relative;
  &:after {
    content: " ";
    background-color: transparent;
    position: absolute;
    right: 0;
    width: 4px;
    height: 100%;
    cursor: w-resize;
  }
}

.gTtTemplate{
  font-size: 12px;
}

.gtaskred {
  background: rgb(196, 58, 58);
  background: #FB3738;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ef4c4c', endColorstr='#c43a3a', GradientType=0);
}

.gTaskInfo {
  background: #fff;
  width: 400px;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 10px;
  float: left;
  box-shadow: 0px 0px 20px -12px;
}

.ggroupblack, .bg-maroon, .bg-yellow, .bg-teal, .bg-olive, .bg-gray, .bg-gray-dark, .bg-black, .bg-blue-1, .bg-blue-2, .bg-blue-3, .bg-blue-4, .bg-blue-5, .bg-blue-6{
  height: 8px;
  border-radius: 5px;
}
.bg-blue-1 {
  background-color: rgb(0, 119, 255);
}
.text-blue-1 {
  color: rgb(0, 119, 255);
}
a.level1{
  .text-black {
    font-weight: 400;
  }
}
.bg-blue-2 {
  background-color: rgb(111, 177, 252);
}
.text-blue-2 {
  color: rgb(111, 177, 252);
}
.bg-blue-3 {
  background-color: rgb(68, 107, 167);
}
.text-blue-3 {
  color: rgb(68, 107, 167);
}
.bg-blue-4 {
  background-color: rgb(67, 96, 122);
}
.text-blue-4 {
  color: rgb(67, 96, 122);
}
.bg-blue-5 {
  background-color: rgb(100, 118, 126);
}
.text-blue-5 {
  color: rgb(100, 118, 126);
}
.bg-blue-6 {
  background-color: rgb(146, 146, 146);
}
.text-blue-6 {
  color: rgb(146, 146, 146);
}
.bg-marooncomplete, .bg-yellowcomplete, .bg-tealcomplete, .bg-olivecomplete, .bg-graycomplete, .bg-gray-darkcomplete, .bg-blackcomplete,
.bg-blue-1complete, .bg-blue-2complete, .bg-blue-3complete, .bg-blue-4complete, .bg-blue-5complete, .bg-blue-6complete{
  float: left;
  height: 2px;
  background: white;
  position: relative;
  top: 3px;
  opacity: 0.9;
}

.text-black {
  color: black !important;
}

.ggroupblackendpointright, .ggroupblackendpointleft {
  display: none;
}

.ggroupblack {
  background:#47dafc;
}

.ggroupblackcomplete {
  height: 2px;
  background: #007bff;
  position: relative;
  top: 1px;
  opacity: 0.9;
}

.gitemhighlight, .gitemhighlight td {
  background-color: #F0F0F0;
}

.gchartcontainer {
  border: 1px solid #f3f3f3;
  margin-top: 15px;
}

.gtasktableh tr, .gcharttable tr, .gcharttableh tr, .gtasktable tr {
  height: 25px !important;
}

.gmajorheading{
  min-width: 196px;
}
.gcharttable td {
  width: 28px !important;
}

.gtasktable {
  .gtaskcellcols {
    border-left: 0;
  }
}

.ggroupitem {
  background-color: white;
  font-weight: 300;
  a:not([class]) {
    font-weight: 600;
  }
}
.glineitem{
  font-weight: 300;
}

.gchartcontainer {
  height: auto !important;
}

.gtasktableh {
  .gspanning {
    overflow: unset;
    .gselector {
      font-size: 15px;
    }
  } 
}

.gminorheadingwkend, .gtaskcell, .gtaskcellcurrent, .gtaskcellwkend,.gcharttable td {
  width: 28px !important;
}
.gminorheading div{
  width: 28px !important;
}
.gminorheading{
  width: 28px !important;
}
.gTaskInfo {
  background: #5B7896 !important;
  color: rgba(255, 255, 255, 0.87);
  border-radius: 10px;
  a {
    color: rgba(255, 255, 255, 0.87);
  }
}

.gcharttable {
  .gtaskcellcurrent {
    background-color: #e1e0f7 !important;
  }
}

.progress-zero {
  .ggroupblackcomplete, .bg-blue-1complete, .bg-blue-2complete,.bg-blue-3complete, .bg-blue-4complete,.bg-blue-5complete, .bg-blue-6complete {
    max-width: 0;
  }
}
.progress-zero + .ggroupcaption {
  visibility: hidden;
  &::before {
    content:'0%'; 
    visibility: visible;
  }
}
.progress-zero + .gcaption {
  visibility: hidden;
  &::before {
    content:'0%'; 
    visibility: visible;
  }
}
