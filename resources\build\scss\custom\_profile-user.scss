.content-infomation {
    padding-top: 10px !important;
    .card-profile-information {
        width: 366px;
        min-width: 220px;
        .card-outline {
            border: none;
            margin: 30px 0 0 0 !important;
            .box-profile {
                .profile-username {
                    margin-top: 18px;
                    font-size: 16px;
                    color: #4d4d4d !important;
                }
                .profile-user-img {
                    padding: 0;
                    border: 2px solid #fafafa;
                }
            }
            .list-group {
                .list-group-item {
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    gap: 8px;
                    span{
                        overflow: hidden;
                    }
                    strong {
                        font-weight: 400;
                        font-size: 16px;
                        color: #464a53;
                    }
                    a {
                        color: #464a53;
                        font-size: 16px;
                        display: flex;
                        width: 100%;
                        justify-content: space-between;
                        -webkit-box-orient:vertical;
                        text-align: left;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        i {
                            color: #ccc;
                        }
                    }
                }
            }
        }
    }
    .theia-content {
        padding-top: 30px !important;
        margin-top: 4px;
        #authTasks,
        #authProjects {
            border-radius: 12px;
            padding: 26px 12px;
            background-color: #fff;
            .card-header {
                background-color: #fff;
                border: none;
                .card-title {
                    font-size: 22px !important;
                }
            }
            .card-footer {
                border: none;
            }
            .table-responsive {
                border: 1px solid #dddfe1 !important;
                border-radius: 10px;
                overflow: hidden;
                overflow-x: auto;
                table {
                    border: none;
                    thead {
                        tr {
                            th {
                                background-color: #fafafa;
                                color: #6a707e;
                                border-top: 0;
                                span {
                                    font-weight: 600 !important;
                                }
                                &:last-child {
                                    border-right: 0;
                                }
                                &:first-child {
                                    border-left: 0;
                                }
                            }
                        }
                    }
                    tbody {
                        tr {
                            background-color: #fff;
                            &:nth-child(2n) {
                                background-color: #fafafa !important;
                            }
                            td {
                                font-weight: 500 !important;
                                color: #000000;
                                strong {
                                    font-weight: 500;
                                }
                                &:last-child {
                                    border-right: 0;
                                }
                                &:first-child {
                                    border-left: 0;
                                }
                            }
                        }
                    }
                    tfoot {
                        tr {
                            td {
                                color: #000000 !important;
                                border-bottom: 0;
                                &:last-child {
                                    border-right: 0;
                                }
                                &:first-child {
                                    border-left: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
        #authProjects .table-responsive table tbody tr td {
            border-bottom: 0;
        }
        #authActivities {
            .card-header {
                background-color: #fff;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                .card-title {
                    font-size: 22px !important;
                }
            }
            .card-body{
                .btn-refresh{
                    background-color: $blue;
                    color: #fff !important;
                    &:hover{
                        background-color: #088493;
                    }
                }
                a.collapseFilter{
                    background-color: #dddfe1;
                    color: #000000 !important;
                    &:hover{
                       background-color: #7C7C7C;
                       color: #fff !important;
                    }
                }
            }
            .auth-activities{
                .list-activities{
                    .page-item{
                        .page-link{
                            &:hover{
                                background-color: #35c8d8;
                                border: 1px solid #35c8d8;
                            }
                        }
                    }
                    .page-item.active{
                        .page-link  {
                            background-color: $blue;
                            border-color: $blue;
                        }
                    }
                }
            } 
        }
    }
}
.text-black {
    color: #4d4d4d;
}
