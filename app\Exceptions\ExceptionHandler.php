<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;


class ExceptionHandler
{
    /**
     * Render the exception as an HTTP response.
     */
    public function render(Exception $e, Request $request)
    {
        if ($request->is('api/*')) {
            if ($e instanceof \Illuminate\Auth\AuthenticationException) {
                $statusCode = Response::HTTP_UNAUTHORIZED;
                $response = [
                    'code' => $statusCode,
                    'message' => 'Unauthorized'
                ];
            } else if ($e instanceof ValidationException) {
                $msg = '';
                foreach ($e->errors() as $field => $errors) {
                    foreach ($errors as $key => $value) {
                        $msg = $value;
                        break;
                    }
                    if ($msg) break;
                }
                $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY;
                $response = [
                    'code' => $statusCode,
                    'message' => $msg
                ];
            } else {
                $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR;
                $response = [
                    'code' => $statusCode,
                    'message' => trans('message.server_error')
                ];
            }
            return response()->json($response, $statusCode);
        }
    }
}
