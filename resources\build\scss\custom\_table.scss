.table-custom{
  background: #fff;
  table-layout: fixed;
  thead{
    background: $gray-200;
    th{
      position: relative;
      a{
        color: inherit;
        display: block;
        padding: 0 0.5rem;
      }
      i{
        position: absolute;
        right: 5px;
        top: 50%;
        @include translate(0, -50%)
      }
    }
  }
  &, &.table-striped{
    tbody{
      tr{
        transition: all linear .2s;
        &:hover, &:nth-of-type(odd):hover{
          background: #fffbf0;
        }
      }
      td{
        padding: 5px 10px;
        text-overflow: ellipsis; 
          overflow: hidden; 
          white-space: nowrap;
          &.user-name{
            white-space: normal;
            span{
              text-overflow: ellipsis; 
              overflow: hidden; 
              white-space: nowrap;
            }
          }
      }
    }
  }

  &.table-striped{
    tbody{
      tr:nth-of-type(odd){
        background-color: #fafafa;
      }
    }
  }

  &.min-width-600{
    min-width: 700px;
  }
  &.min-width-800{
    min-width: 800px;
  }
  &.min-width-1000{
    min-width: 1000px;
  }
  &.min-width-1200{
    min-width: 1200px;
  }
}

.width-50{
  width:50px;
}

.table-add-member{
  height: 60vh!important; 
  overflow-y: auto;
  .td-add-member{
    display: flex;
    justify-content: space-between;
    .add-member-left{
      display: flex;
    }
    .action-add-member{
      button{
        color: rgba(243, 229, 229, 0.925);
        padding: 0px;
      }
      .editpos{
        margin-right: 10px;
        margin-left: 10px;
        height: 30px;
        width: 30px;
        min-width: unset;
        border-radius: 50%;
        &:hover{
          background-color: #17a2b8;
          color: white;
          border-radius: 50%;
          height: 30px;
          width: 30px;
        }
      }
      .delete-member{
        min-width: unset;
        height: 30px;
        width: 30px;
        border-radius: 50%;
        &:hover{
          background-color: #17a2b8;
          color: white;
          border-radius: 50%;
          height: 30px;
          width: 30px;
        }
      }
    }
    .name-position{
      white-space: pre-wrap;
    }
  }
  tr:hover{
    .action-add-member{
      .editpos{
        color: #17a2b8;
      }
      .delete-member{
        color: #dc3545;
      }
    }
  }
}
.scroll{
  max-height: 500px;
  overflow: auto;
}

.table-projects {
    .table {
        &.min-width-600{
            min-width: 700px;
        }
        &.min-width-800{
            min-width: 800px;
        }
        &.min-width-1000{
            min-width: 1000px;
        }
        &.min-width-1200{
            min-width: 1200px;
        }
    }
}

@media (max-width: 1290px) and (min-width: 1200px) {
  .table-add-member{
    .td-add-member{
      flex-direction: column;
    }
  }

  .table-custom tbody td, .table-custom.table-striped tbody td {
    white-space: unset;
  }
}