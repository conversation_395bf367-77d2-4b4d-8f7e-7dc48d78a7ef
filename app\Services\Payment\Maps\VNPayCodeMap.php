<?php

namespace App\Services\Payment\Maps;

/**
 * Maps VNPay codes to internal PaymentStatus and human-readable reasons.
 * Reference: VNPay official error table (vnp_ResponseCode, queryDr, refund).
 */
final class VNPayCodeMap
{
    /** vnp_ResponseCode on Return/IPN */
    public static function responseCode(?string $code): string
    {
        $map = [
            '00' => 'Giao dịch thành công',
            '07' => 'Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).',
            '09' => 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng.',
            '10' => 'Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần',
            '11' => '<PERSON>ia<PERSON> dịch không thành công do: <PERSON><PERSON> hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.',
            '12' => 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.',
            '13' => 'Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP). Xin quý khách vui lòng thực hiện lại giao dịch.',
            '15' => 'Giao dịch hết hạn',
            '24' => 'Giao dịch không thành công do: Khách hàng hủy giao dịch',
            '51' => 'Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.',
            '65' => 'Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.',
            '75' => 'Ngân hàng thanh toán đang bảo trì.',
            '79' => 'Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định. Xin quý khách vui lòng thực hiện lại giao dịch',
            '99' => 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)',
        ];
        return $map[$code] ?? 'Unmapped VNPay RETURN/IPN code';
    }

    /** queryDr response codes */
    public static function queryDr(?string $code): string
    {
        $map = [
            '00' => 'Yêu cầu thành công',
            '02' => 'Mã định danh kết nối không hợp lệ (kiểm tra lại TmnCode)',
            '03' => 'Dữ liệu gửi sang không đúng định dạng',
            '91' => 'Không tìm thấy giao dịch yêu cầu',
            '94' => 'Yêu cầu trùng lặp, duplicate request trong thời gian giới hạn của API',
            '97' => 'Checksum không hợp lệ',
            '99' => 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)'
        ];
        return $map[$code] ?? 'Unmapped VNPay queryDr';
    }

    /** refund response codes (optional) */
    public static function refund(?string $code): string
    {
        $map = [
            '00' => 'Yêu cầu thành công',
            '02' => 'Mã định danh kết nối không hợp lệ (kiểm tra lại TmnCode)',
            '03' => 'Dữ liệu gửi sang không đúng định dạng',
            '91' => 'Không tìm thấy giao dịch yêu cầu hoàn trả',
            '94' => 'Giao dịch đã được gửi yêu cầu hoàn tiền trước đó. Yêu cầu này VNPAY đang xử lý',
            '95' => 'Giao dịch này không thành công bên VNPAY. VNPAY từ chối xử lý yêu cầu',
            '97' => 'Checksum không hợp lệ',
            '99' => 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)'
        ];
        return $map[$code] ?? 'Unmapped VNPay refund';
    }

    public static function transactionStatus(?string $code): string
    {
        $map = [
            '00' => 'Giao dịch thanh toán thành công',
            '01' => 'Giao dịch chưa hoàn tất',
            '02' => 'Giao dịch bị lỗi',
            '04' => 'Giao dịch đảo (Khách hàng đã bị trừ tiền tại Ngân hàng nhưng GD chưa thành công ở VNPAY)',
            '05' => 'VNPAY đang xử lý giao dịch này (GD hoàn tiền)',
            '06' => 'VNPAY đã gửi yêu cầu hoàn tiền sang Ngân hàng (GD hoàn tiền)',
            '07' => 'Giao dịch bị nghi ngờ gian lận',
            '09' => 'GD Hoàn trả bị từ chối'
        ];
        return $map[$code] ?? 'Unmapped VNPay transactionStatus';
    }
}
