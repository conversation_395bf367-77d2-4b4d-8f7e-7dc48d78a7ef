<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Enums\OrderType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id','type','code','amount','currency','status','meta','paid_at'
    ];

    protected $casts = [
        'type'    => OrderType::class,
        'status'  => OrderStatus::class,
        'meta'    => 'array',
        'paid_at' => 'datetime',
    ];

    public function payments() {
        return $this->hasMany(Payment::class);
    }

    protected static function booted()
    {
        static::creating(function ($order) {
            if (empty($order->code)) {
                $order->code = self::generateUniqueCode();
            }
        });
    }

    public static function generateUniqueCode(): string
    {
        do {
            $date = now()->format('ymd');
            $random = Str::upper(Str::random(8));
            $code = 'ORD' . $date . $random;
        } while (self::where('code', $code)->exists());

        return $code;
    }
}
